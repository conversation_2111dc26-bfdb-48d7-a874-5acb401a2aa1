<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <!-- 定义日志级别变量，支持系统属性覆盖 -->
    <property name="RASP_LOG_LEVEL" value="${rasp.log.level:-INFO}" />
    <property name="RASP_LOG_FILE" value="${rasp.log.file:-logs/rasp-agent.log}" />

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 文件输出 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${RASP_LOG_FILE}</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${RASP_LOG_FILE}.%d{yyyy-MM-dd}</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- RASP相关包的日志级别 - 使用动态级别 -->
    <logger name="com.rasp.core" level="${RASP_LOG_LEVEL}" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </logger>

    <logger name="com.rasp.hooks" level="${RASP_LOG_LEVEL}" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </logger>

    <logger name="com.rasp.rules" level="${RASP_LOG_LEVEL}" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </logger>

    <!-- 特定类的日志级别控制 - 可以通过系统属性单独覆盖 -->
    <logger name="com.rasp.core.RaspCore" level="${rasp.log.level.core:-${RASP_LOG_LEVEL}}"/>
    <logger name="com.rasp.core.rule.RuleEngine" level="${rasp.log.level.rule:-${RASP_LOG_LEVEL}}"/>
    <logger name="com.rasp.core.hook.HookManager" level="${rasp.log.level.hook:-${RASP_LOG_LEVEL}}"/>
    <logger name="com.rasp.core.enhance.ClassEnhancer" level="${rasp.log.level.enhance:-WARN}"/>

    <!-- 根日志级别 -->
    <root level="${RASP_LOG_LEVEL}">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </root>

</configuration>
