# RASP规则配置指南

## 概述

RASP Agent现在支持通过配置文件来管理规则的启用和禁用状态，提供了更灵活的规则管理方式。

## 配置文件位置

RASP Agent会按以下优先级查找配置文件：

1. **系统属性指定的配置文件**: `-Drasp.config.file=/path/to/config.properties`
2. **类路径下的默认配置文件**: `rasp-config.properties`
3. **config目录下的配置文件**: `config/rasp-config.properties`

## 配置文件格式

### 规则配置格式

```properties
# 格式: rasp.rule.<RuleName>.enabled=true/false
rasp.rule.HttpRequestLogRule.enabled=true
rasp.rule.CommandExecutionRule.enabled=true
rasp.rule.FileWriteRule.enabled=true
rasp.rule.MethodCallLogRule.enabled=false
```

### 可用规则列表

| 规则名称 | 描述 | 默认状态 | 建议使用场景 |
|---------|------|----------|-------------|
| `HttpRequestLogRule` | HTTP请求日志记录 | 启用 | 监控HTTP请求，适用于所有环境 |
| `CommandExecutionRule` | 命令执行安全检测 | 启用 | 检测危险命令执行，生产环境必需 |
| `FileWriteRule` | 文件写入监控 | 启用 | 监控文件操作，安全审计 |
| `MethodCallLogRule` | 方法调用日志 | 禁用 | 详细调试，仅开发环境使用 |

## 使用方式

### 1. 基本配置

创建 `rasp-config.properties` 文件：

```properties
# 启用HTTP请求监控
rasp.rule.HttpRequestLogRule.enabled=true

# 启用命令执行检测
rasp.rule.CommandExecutionRule.enabled=true

# 禁用详细的方法调用日志
rasp.rule.MethodCallLogRule.enabled=false
```

### 2. 环境特定配置

#### 开发环境配置
```bash
# 使用开发环境配置
java -javaagent:rasp-agent.jar \
     -Drasp.config.file=config/rasp-config-dev.properties \
     -jar your-app.jar
```

#### 生产环境配置
```bash
# 使用生产环境配置
java -javaagent:rasp-agent.jar \
     -Drasp.config.file=config/rasp-config-prod.properties \
     -jar your-app.jar
```

### 3. 运行时配置管理

#### 使用工具类管理规则

```java
import com.rasp.core.config.RuleConfigUtil;

// 打印当前规则状态
RuleConfigUtil.printRuleStatus();

// 启用/禁用特定规则
RuleConfigUtil.enableRule("MethodCallLogRule");
RuleConfigUtil.disableRule("HttpRequestLogRule");

// 批量操作
RuleConfigUtil.enableRules("HttpRequestLogRule", "CommandExecutionRule");
RuleConfigUtil.disableAllLogRules();

// 环境模式切换
RuleConfigUtil.setDevelopmentMode();  // 开发模式
RuleConfigUtil.setProductionMode();   // 生产模式

// 重新加载配置
RuleConfigUtil.reloadConfiguration();
```

## 配置示例

### 开发环境配置示例

```properties
# 开发环境 - 启用详细日志
rasp.enabled=true
rasp.log.level=DEBUG

# 规则配置 - 开发模式
rasp.rule.HttpRequestLogRule.enabled=true
rasp.rule.CommandExecutionRule.enabled=true
rasp.rule.FileWriteRule.enabled=true
rasp.rule.MethodCallLogRule.enabled=true  # 开发环境启用详细日志

# 性能配置 - 开发环境相对宽松
rasp.performance.max_events_per_second=2000
rasp.debug.enabled=true
```

### 生产环境配置示例

```properties
# 生产环境 - 优化性能
rasp.enabled=true
rasp.log.level=WARN

# 规则配置 - 生产模式
rasp.rule.HttpRequestLogRule.enabled=true
rasp.rule.CommandExecutionRule.enabled=true
rasp.rule.FileWriteRule.enabled=true
rasp.rule.MethodCallLogRule.enabled=false  # 生产环境禁用详细日志

# 性能配置 - 生产环境严格限制
rasp.performance.max_events_per_second=500
rasp.debug.enabled=false
```

## 配置验证

### 检查配置是否生效

1. **查看启动日志**:
```
INFO  c.r.c.config.RaspConfigManager - Configuration loaded from: rasp-config.properties
INFO  c.r.c.config.RaspConfigManager - Rule HttpRequestLogRule: ENABLED
INFO  c.r.c.config.RaspConfigManager - Rule CommandExecutionRule: ENABLED
INFO  c.r.c.config.RaspConfigManager - Rule MethodCallLogRule: DISABLED
```

2. **运行时检查**:
```java
// 检查规则状态
boolean enabled = RuleConfigUtil.isRuleEnabled("HttpRequestLogRule");
System.out.println("HttpRequestLogRule enabled: " + enabled);

// 打印所有规则状态
RuleConfigUtil.printRuleStatus();
```

## 最佳实践

### 1. 环境分离
- 为不同环境创建专门的配置文件
- 使用系统属性指定环境特定的配置文件

### 2. 性能考虑
- 生产环境禁用 `MethodCallLogRule`（非常详细，影响性能）
- 根据应用负载调整 `max_events_per_second`

### 3. 安全考虑
- 生产环境必须启用 `CommandExecutionRule`
- 根据安全需求启用 `FileWriteRule`

### 4. 监控和调试
- 开发环境启用详细日志规则
- 使用 `RuleConfigUtil` 进行运行时管理

## 故障排除

### 配置文件未生效
1. 检查配置文件路径是否正确
2. 检查配置文件格式是否正确
3. 查看启动日志中的配置加载信息

### 规则未按预期工作
1. 使用 `RuleConfigUtil.printRuleStatus()` 检查规则状态
2. 检查日志级别设置
3. 验证规则名称是否正确

### 性能问题
1. 检查是否启用了 `MethodCallLogRule`
2. 调整 `max_events_per_second` 参数
3. 在生产环境禁用调试模式

## 扩展配置

### 添加新规则配置
当添加新的规则时，只需在配置文件中添加相应的配置项：

```properties
# 新规则配置
rasp.rule.NewCustomRule.enabled=true
```

### 自定义配置加载
可以通过系统属性或环境变量覆盖配置：

```bash
# 通过系统属性覆盖
-Drasp.rule.MethodCallLogRule.enabled=true

# 通过环境变量（需要代码支持）
export RASP_RULE_METHODCALLLOGRULE_ENABLED=true
```
