package com.rasp.api.context;

import com.rasp.api.event.HttpRequestEvent;

/**
 * 请求上下文管理器
 * 用于在同一个请求线程中共享HTTP请求信息
 */
public class RequestContext {
    
    /**
     * 线程本地存储，保存当前线程的HTTP请求事件
     */
    private static final ThreadLocal<HttpRequestEvent> HTTP_REQUEST_CONTEXT = new ThreadLocal<>();
    
    /**
     * 设置当前线程的HTTP请求事件
     * @param httpRequestEvent HTTP请求事件
     */
    public static void setHttpRequestEvent(HttpRequestEvent httpRequestEvent) {
        HTTP_REQUEST_CONTEXT.set(httpRequestEvent);
    }
    
    /**
     * 获取当前线程的HTTP请求事件
     * @return HTTP请求事件，如果没有则返回null
     */
    public static HttpRequestEvent getHttpRequestEvent() {
        return HTTP_REQUEST_CONTEXT.get();
    }
    
    /**
     * 清除当前线程的HTTP请求事件
     */
    public static void clearHttpRequestEvent() {
        HTTP_REQUEST_CONTEXT.remove();
    }
    
    /**
     * 检查当前线程是否有HTTP请求上下文
     * @return 如果有HTTP请求上下文返回true，否则返回false
     */
    public static boolean hasHttpRequestContext() {
        return HTTP_REQUEST_CONTEXT.get() != null;
    }
    
    /**
     * 获取当前请求的客户端IP
     * @return 客户端IP，如果没有请求上下文则返回null
     */
    public static String getClientIp() {
        HttpRequestEvent event = getHttpRequestEvent();
        return event != null ? event.getClientIp() : null;
    }
    
    /**
     * 获取当前请求的URL
     * @return 请求URL，如果没有请求上下文则返回null
     */
    public static String getRequestUrl() {
        HttpRequestEvent event = getHttpRequestEvent();
        return event != null ? event.getRequestUrl() : null;
    }
    
    /**
     * 获取当前请求的方法
     * @return 请求方法，如果没有请求上下文则返回null
     */
    public static String getRequestMethod() {
        HttpRequestEvent event = getHttpRequestEvent();
        return event != null ? event.getRequestMethod() : null;
    }
    
    /**
     * 获取当前请求的User-Agent
     * @return User-Agent，如果没有请求上下文则返回null
     */
    public static String getUserAgent() {
        HttpRequestEvent event = getHttpRequestEvent();
        return event != null ? event.getUserAgent() : null;
    }
    
    /**
     * 检查请求参数中是否包含指定的值
     * @param value 要检查的值
     * @return 如果参数中包含该值返回true，否则返回false
     */
    public static boolean containsParameterValue(String value) {
        HttpRequestEvent event = getHttpRequestEvent();
        if (event == null || event.getRequestParameters() == null || value == null) {
            return false;
        }
        
        for (String[] paramValues : event.getRequestParameters().values()) {
            if (paramValues != null) {
                for (String paramValue : paramValues) {
                    if (paramValue != null && paramValue.contains(value)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }
    
    /**
     * 检查查询字符串中是否包含指定的值
     * @param value 要检查的值
     * @return 如果查询字符串中包含该值返回true，否则返回false
     */
    public static boolean containsQueryStringValue(String value) {
        HttpRequestEvent event = getHttpRequestEvent();
        if (event == null || event.getQueryString() == null || value == null) {
            return false;
        }
        
        return event.getQueryString().contains(value);
    }
    
    /**
     * 检查请求体中是否包含指定的值
     * @param value 要检查的值
     * @return 如果请求体中包含该值返回true，否则返回false
     */
    public static boolean containsRequestBodyValue(String value) {
        HttpRequestEvent event = getHttpRequestEvent();
        if (event == null || event.getRequestBody() == null || value == null) {
            return false;
        }
        
        return event.getRequestBody().contains(value);
    }
    
    /**
     * 检查HTTP请求中是否包含指定的值（参数、查询字符串、请求体）
     * @param value 要检查的值
     * @return 如果HTTP请求中包含该值返回true，否则返回false
     */
    public static boolean containsInHttpRequest(String value) {
        return containsParameterValue(value) || 
               containsQueryStringValue(value) || 
               containsRequestBodyValue(value);
    }
}
