# Hook开发实战示例：SQL注入检测Hook

## 概述

本文档以开发SQL注入检测Hook为例，展示如何基于RASP Hook开发框架快速实现新的安全检测功能。

## 需求分析

**目标**: 检测和阻止SQL注入攻击
**拦截点**: JDBC相关类的executeQuery、executeUpdate等方法
**检测逻辑**: 分析SQL语句，识别可能的SQL注入模式

## 开发实现

### 第一步：设计事件对象

```java
package com.rasp.api.event;

/**
 * SQL执行事件
 */
public class SqlExecutionEvent extends HookEvent {
    
    // SQL相关信息
    private String sql;                    // 原始SQL语句
    private String normalizedSql;          // 标准化SQL（移除字面量）
    private String[] parameters;           // 参数列表
    private String databaseUrl;            // 数据库连接URL
    private String databaseType;           // 数据库类型
    
    // 安全分析字段
    private boolean hasUserInput;          // 是否包含用户输入
    private String[] suspiciousKeywords;   // 可疑关键词
    private int parameterCount;            // 参数数量
    
    // Getters and Setters
    public String getSql() { return sql; }
    public void setSql(String sql) { this.sql = sql; }
    
    public String getNormalizedSql() { return normalizedSql; }
    public void setNormalizedSql(String normalizedSql) { this.normalizedSql = normalizedSql; }
    
    public String[] getParameters() { return parameters; }
    public void setParameters(String[] parameters) { this.parameters = parameters; }
    
    public String getDatabaseUrl() { return databaseUrl; }
    public void setDatabaseUrl(String databaseUrl) { this.databaseUrl = databaseUrl; }
    
    public String getDatabaseType() { return databaseType; }
    public void setDatabaseType(String databaseType) { this.databaseType = databaseType; }
    
    public boolean isHasUserInput() { return hasUserInput; }
    public void setHasUserInput(boolean hasUserInput) { this.hasUserInput = hasUserInput; }
    
    public String[] getSuspiciousKeywords() { return suspiciousKeywords; }
    public void setSuspiciousKeywords(String[] suspiciousKeywords) { this.suspiciousKeywords = suspiciousKeywords; }
    
    public int getParameterCount() { return parameterCount; }
    public void setParameterCount(int parameterCount) { this.parameterCount = parameterCount; }
    
    @Override
    public String toString() {
        return "SqlExecutionEvent{" +
                "sql='" + sql + '\'' +
                ", databaseType='" + databaseType + '\'' +
                ", hasUserInput=" + hasUserInput +
                ", parameterCount=" + parameterCount +
                '}';
    }
}
```

**设计要点**：
1. **SQL标准化**: 移除字面量值，便于模式匹配
2. **参数分离**: 将SQL和参数分开，便于注入检测
3. **上下文信息**: 包含数据库类型、连接信息等上下文
4. **预分析**: 在事件层就完成基础的安全分析

### 第二步：实现Hook拦截逻辑

```java
package com.rasp.hooks.sql;

import com.rasp.api.event.HookEvent;
import com.rasp.api.event.SqlExecutionEvent;
import com.rasp.api.hook.AbstractHook;
import com.rasp.api.context.RequestContext;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * SQL执行Hook
 */
public class SqlExecutionHook extends AbstractHook {
    
    // SQL注入相关关键词
    private static final String[] SUSPICIOUS_KEYWORDS = {
        "union", "select", "insert", "update", "delete", "drop", "create",
        "alter", "exec", "execute", "sp_", "xp_", "or", "and", "||", "&&",
        "--", "/*", "*/", "char", "ascii", "substring", "length", "user",
        "database", "version", "@@", "waitfor", "delay"
    };
    
    // SQL标准化模式
    private static final Pattern NORMALIZE_PATTERN = Pattern.compile(
        "'[^']*'|\"[^\"]*\"|\\b\\d+\\b", Pattern.CASE_INSENSITIVE);
    
    @Override
    public String getName() {
        return "SqlExecutionHook";
    }
    
    @Override
    public String getDescription() {
        return "Hook for detecting SQL injection attacks";
    }
    
    @Override
    public String[] getClassNamePatterns() {
        return new String[]{
            "java.sql.Statement",
            "java.sql.PreparedStatement",
            "java.sql.CallableStatement",
            "com.mysql.cj.jdbc.*",
            "oracle.jdbc.*",
            "com.microsoft.sqlserver.jdbc.*"
        };
    }
    
    @Override
    public String[] getMethodNamePatterns() {
        return new String[]{
            "executeQuery",
            "executeUpdate", 
            "execute",
            "executeBatch"
        };
    }
    
    @Override
    public HookEvent onMethodEnter(String className, String methodName, String methodSignature,
                                  Object target, Object[] arguments) {
        
        SqlExecutionEvent event = new SqlExecutionEvent();
        event.setEventType(HookEvent.EventType.SQL_EXECUTION);
        event.setClassName(className);
        event.setMethodName(methodName);
        event.setMethodSignature(methodSignature);
        event.setArguments(arguments);
        
        try {
            // 提取SQL语句
            String sql = extractSqlStatement(target, arguments, methodName);
            if (sql != null) {
                event.setSql(sql);
                
                // SQL标准化处理
                String normalizedSql = normalizeSql(sql);
                event.setNormalizedSql(normalizedSql);
                
                // 提取数据库信息
                extractDatabaseInfo(target, event);
                
                // 分析可疑关键词
                String[] suspiciousKeywords = analyzeSuspiciousKeywords(sql);
                event.setSuspiciousKeywords(suspiciousKeywords);
                
                // 检查是否包含用户输入
                boolean hasUserInput = checkUserInput(sql);
                event.setHasUserInput(hasUserInput);
                
                // 提取参数信息
                extractParameterInfo(target, event);
            }
            
        } catch (Exception e) {
            logger.error("Error processing SQL execution", e);
        }
        
        return event;
    }
    
    /**
     * 提取SQL语句
     */
    private String extractSqlStatement(Object target, Object[] arguments, String methodName) {
        // 对于executeQuery(String sql)等方法，SQL在第一个参数中
        if (arguments != null && arguments.length > 0 && arguments[0] instanceof String) {
            return (String) arguments[0];
        }
        
        // 对于PreparedStatement，需要通过反射获取
        if (target != null) {
            try {
                // 尝试获取SQL字段
                java.lang.reflect.Field sqlField = findSqlField(target.getClass());
                if (sqlField != null) {
                    sqlField.setAccessible(true);
                    Object sqlObj = sqlField.get(target);
                    if (sqlObj instanceof String) {
                        return (String) sqlObj;
                    }
                }
            } catch (Exception e) {
                logger.debug("Failed to extract SQL from PreparedStatement", e);
            }
        }
        
        return null;
    }
    
    /**
     * 查找SQL字段
     */
    private java.lang.reflect.Field findSqlField(Class<?> clazz) {
        // 常见的SQL字段名
        String[] fieldNames = {"sql", "originalSql", "nativeSql", "query"};
        
        for (String fieldName : fieldNames) {
            try {
                return clazz.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                // 继续尝试下一个字段名
            }
        }
        
        // 在父类中查找
        Class<?> superClass = clazz.getSuperclass();
        if (superClass != null && !superClass.equals(Object.class)) {
            return findSqlField(superClass);
        }
        
        return null;
    }
    
    /**
     * SQL标准化处理
     */
    private String normalizeSql(String sql) {
        if (sql == null) return null;
        
        // 移除字符串字面量和数字字面量
        return NORMALIZE_PATTERN.matcher(sql).replaceAll("?").toLowerCase().trim();
    }
    
    /**
     * 提取数据库信息
     */
    private void extractDatabaseInfo(Object target, SqlExecutionEvent event) {
        try {
            // 获取Connection对象
            Connection connection = null;
            if (target instanceof Connection) {
                connection = (Connection) target;
            } else {
                // 通过反射获取Connection
                java.lang.reflect.Method getConnectionMethod = target.getClass().getMethod("getConnection");
                Object connObj = getConnectionMethod.invoke(target);
                if (connObj instanceof Connection) {
                    connection = (Connection) connObj;
                }
            }
            
            if (connection != null) {
                String url = connection.getMetaData().getURL();
                event.setDatabaseUrl(url);
                
                // 推断数据库类型
                String dbType = inferDatabaseType(url);
                event.setDatabaseType(dbType);
            }
            
        } catch (Exception e) {
            logger.debug("Failed to extract database info", e);
        }
    }
    
    /**
     * 推断数据库类型
     */
    private String inferDatabaseType(String url) {
        if (url == null) return "unknown";
        
        String lowerUrl = url.toLowerCase();
        if (lowerUrl.contains("mysql")) return "mysql";
        if (lowerUrl.contains("oracle")) return "oracle";
        if (lowerUrl.contains("sqlserver")) return "sqlserver";
        if (lowerUrl.contains("postgresql")) return "postgresql";
        if (lowerUrl.contains("h2")) return "h2";
        if (lowerUrl.contains("sqlite")) return "sqlite";
        
        return "unknown";
    }
    
    /**
     * 分析可疑关键词
     */
    private String[] analyzeSuspiciousKeywords(String sql) {
        if (sql == null) return new String[0];
        
        List<String> found = new ArrayList<>();
        String lowerSql = sql.toLowerCase();
        
        for (String keyword : SUSPICIOUS_KEYWORDS) {
            if (lowerSql.contains(keyword.toLowerCase())) {
                found.add(keyword);
            }
        }
        
        return found.toArray(new String[0]);
    }
    
    /**
     * 检查是否包含用户输入
     */
    private boolean checkUserInput(String sql) {
        RequestContext context = RequestContext.getCurrentRequest();
        if (context == null) return false;
        
        // 检查SQL是否包含请求参数值
        if (context.getParameterMap() != null) {
            for (String[] values : context.getParameterMap().values()) {
                for (String value : values) {
                    if (value != null && sql.contains(value)) {
                        return true;
                    }
                }
            }
        }
        
        return false;
    }
    
    /**
     * 提取参数信息
     */
    private void extractParameterInfo(Object target, SqlExecutionEvent event) {
        // 这里可以通过反射获取PreparedStatement的参数信息
        // 实现比较复杂，简化处理
        event.setParameterCount(0);
    }
}
```

**实现要点**：
1. **多数据库支持**: 覆盖主流数据库的JDBC驱动
2. **智能SQL提取**: 支持动态SQL和预编译语句
3. **上下文关联**: 检查SQL中是否包含HTTP请求参数
4. **SQL标准化**: 便于后续模式匹配和威胁检测

### 第三步：编写安全规则

```java
package com.rasp.rules.sql;

import com.rasp.api.event.HookEvent;
import com.rasp.api.event.SqlExecutionEvent;
import com.rasp.api.rule.AbstractRule;
import com.rasp.api.rule.RuleResult;

import java.util.regex.Pattern;

/**
 * SQL注入检测规则
 */
public class SqlInjectionRule extends AbstractRule {
    
    // SQL注入模式
    private static final Pattern[] INJECTION_PATTERNS = {
        Pattern.compile("union\\s+select", Pattern.CASE_INSENSITIVE),
        Pattern.compile("(or|and)\\s+1\\s*=\\s*1", Pattern.CASE_INSENSITIVE),
        Pattern.compile("(or|and)\\s+1\\s*=\\s*0", Pattern.CASE_INSENSITIVE),
        Pattern.compile("'\\s*(or|and)\\s+'.*'\\s*=\\s*'", Pattern.CASE_INSENSITIVE),
        Pattern.compile("admin'\\s*--", Pattern.CASE_INSENSITIVE),
        Pattern.compile("'\\s*;\\s*(drop|delete|insert|update)", Pattern.CASE_INSENSITIVE),
        Pattern.compile("exec\\s*\\(", Pattern.CASE_INSENSITIVE),
        Pattern.compile("sp_\\w+", Pattern.CASE_INSENSITIVE)
    };
    
    @Override
    public String getName() {
        return "SqlInjectionRule";
    }
    
    @Override
    public String getDescription() {
        return "Detects SQL injection attacks";
    }
    
    @Override
    public boolean isApplicable(HookEvent event) {
        return event instanceof SqlExecutionEvent;
    }
    
    @Override
    public RuleResult evaluate(HookEvent event) {
        SqlExecutionEvent sqlEvent = (SqlExecutionEvent) event;
        String sql = sqlEvent.getSql();
        
        if (sql == null || sql.trim().isEmpty()) {
            return createLogResult("Empty SQL statement");
        }
        
        // 1. 检查明显的SQL注入模式
        for (Pattern pattern : INJECTION_PATTERNS) {
            if (pattern.matcher(sql).find()) {
                return RuleResult.builder()
                    .ruleName(getName())
                    .action(RuleResult.Action.BLOCK)
                    .riskLevel(RuleResult.RiskLevel.CRITICAL)
                    .message("SQL injection pattern detected: " + pattern.pattern())
                    .matched(true)
                    .timestamp(System.currentTimeMillis())
                    .build();
            }
        }
        
        // 2. 检查用户输入相关的SQL注入
        if (sqlEvent.isHasUserInput()) {
            String[] suspiciousKeywords = sqlEvent.getSuspiciousKeywords();
            if (suspiciousKeywords != null && suspiciousKeywords.length > 3) {
                return RuleResult.builder()
                    .ruleName(getName())
                    .action(RuleResult.Action.BLOCK)
                    .riskLevel(RuleResult.RiskLevel.HIGH)
                    .message("Potential SQL injection with user input: " + 
                            String.join(", ", suspiciousKeywords))
                    .matched(true)
                    .timestamp(System.currentTimeMillis())
                    .build();
            }
        }
        
        // 3. 检查异常的SQL结构
        String normalizedSql = sqlEvent.getNormalizedSql();
        if (normalizedSql != null) {
            // 检查是否包含多个SELECT、DROP等危险操作
            long selectCount = countOccurrences(normalizedSql, "select");
            long dropCount = countOccurrences(normalizedSql, "drop");
            long deleteCount = countOccurrences(normalizedSql, "delete");
            
            if (selectCount > 2 || dropCount > 0 || deleteCount > 1) {
                return RuleResult.builder()
                    .ruleName(getName())
                    .action(RuleResult.Action.WARN)
                    .riskLevel(RuleResult.RiskLevel.MEDIUM)
                    .message("Suspicious SQL structure detected")
                    .matched(true)
                    .timestamp(System.currentTimeMillis())
                    .build();
            }
        }
        
        // 4. 正常情况记录
        return createLogResult("SQL execution monitored: " + sqlEvent.getDatabaseType());
    }
    
    /**
     * 计算字符串出现次数
     */
    private long countOccurrences(String text, String pattern) {
        return text.split(pattern, -1).length - 1;
    }
}
```

**规则特点**：
1. **多层检测**: 从模式匹配到结构分析，多维度检测
2. **风险分级**: 根据威胁程度设置不同的风险等级
3. **上下文感知**: 结合用户输入情况进行综合判断
4. **可扩展**: 易于添加新的检测模式和规则

### 第四步：添加事件类型

```java
// 在HookEvent.java中添加新的事件类型
public enum EventType {
    HTTP_REQUEST,
    COMMAND_EXECUTION,
    FILE_WRITE,
    SQL_EXECUTION,    // 新增
    METHOD_CALL
}
```

### 第五步：集成到系统

```java
// 在RaspCore.java中注册
private void initializeHooks() {
    hookManager.registerHook(new HttpRequestHook());
    hookManager.registerHook(new CommandExecutionHook());
    hookManager.registerHook(new FileWriteHook());
    hookManager.registerHook(new SqlExecutionHook());  // 新增
}

// 在RuleEngine中注册规则
private void initializeRules() {
    registerRule(new HttpRequestLogRule());
    registerRule(new CommandExecutionRule());
    registerRule(new MethodCallLogRule());
    registerRule(new FileWriteRule());
    registerRule(new SqlInjectionRule());  // 新增
}
```

## 测试验证

### 单元测试示例

```java
@Test
public void testSqlInjectionDetection() {
    SqlExecutionHook hook = new SqlExecutionHook();
    SqlInjectionRule rule = new SqlInjectionRule();
    
    // 测试SQL注入攻击
    String maliciousSql = "SELECT * FROM users WHERE id = 1 OR 1=1 --";
    
    // 模拟Hook调用
    SqlExecutionEvent event = (SqlExecutionEvent) hook.onMethodEnter(
        "java.sql.Statement", "executeQuery", "(Ljava/lang/String;)Ljava/sql/ResultSet;",
        null, new Object[]{maliciousSql}
    );
    
    // 验证事件数据
    assertEquals(maliciousSql, event.getSql());
    assertTrue(event.getSuspiciousKeywords().length > 0);
    
    // 测试规则检测
    RuleResult result = rule.evaluate(event);
    assertEquals(RuleResult.Action.BLOCK, result.getAction());
    assertEquals(RuleResult.RiskLevel.CRITICAL, result.getRiskLevel());
}
```

## 开发总结

基于RASP Hook开发框架，仅用4个步骤就完成了完整的SQL注入检测功能：

1. **统一架构**: 遵循事件-规则的设计模式
2. **快速开发**: 专注于业务逻辑，无需关心底层细节
3. **易于扩展**: 可以方便地添加新的检测规则
4. **高度可复用**: 框架代码可以复用到其他Hook中

这种模式化的开发方式大大降低了安全Hook的开发难度。 