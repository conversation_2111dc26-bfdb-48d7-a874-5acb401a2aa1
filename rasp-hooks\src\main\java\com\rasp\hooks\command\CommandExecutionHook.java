package com.rasp.hooks.command;

import com.rasp.api.event.HookEvent;
import com.rasp.api.event.CommandExecutionEvent;
import com.rasp.api.hook.AbstractHook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 命令执行Hook
 * 用于捕获系统命令执行
 */
public class CommandExecutionHook extends AbstractHook {

    private static final Logger logger = LoggerFactory.getLogger(CommandExecutionHook.class);

    private boolean enabled = true;
    
    @Override
    public String getName() {
        return "CommandExecutionHook";
    }
    
    @Override
    public String getDescription() {
        return "Hook for capturing command execution";
    }
    
    @Override
    public String[] getClassNamePatterns() {
        return new String[]{
            "java.lang.Runtime",
            "java.lang.ProcessBuilder",
            "java.lang.Process"
        };
    }
    
    @Override
    public String[] getMethodNamePatterns() {
        return new String[]{
            "exec",
            "start"
        };
    }
    
    @Override
    public String[] getMethodSignaturePatterns() {
        return null; // 不限制方法签名
    }
    
    @Override
    public HookEvent onMethodEnter(String className, String methodName, String methodSignature,
                                  Object target, Object[] arguments) {

        logger.debug("[CommandExecutionHook] onMethodEnter called!");
        logger.debug("Class: {}", className);
        logger.debug("Method: {}", methodName);
        logger.debug("Signature: {}", methodSignature);

        CommandExecutionEvent event = new CommandExecutionEvent();
        event.setEventType(HookEvent.EventType.COMMAND_EXECUTION);
        event.setClassName(className);
        event.setMethodName(methodName);
        event.setMethodSignature(methodSignature);
        event.setArguments(arguments);

        logger.debug("Event created with type: {}", event.getEventType());
        
        // 提取命令信息
        try {
            if ("java.lang.Runtime".equals(className) && "exec".equals(methodName)) {
                if (arguments != null && arguments.length > 0) {
                    Object command = arguments[0];
                    if (command instanceof String) {
                        event.setCommand((String) command);
                        logger.debug("Command execution detected: {}", command);
                    } else if (command instanceof String[]) {
                        String[] cmdArray = (String[]) command;
                        if (cmdArray.length > 0) {
                            event.setCommand(String.join(" ", cmdArray));
                            logger.debug("Command execution detected: {}", String.join(" ", cmdArray));
                        }
                    }
                }
            } else if ("java.lang.ProcessBuilder".equals(className) && "start".equals(methodName)) {
                if (target instanceof ProcessBuilder) {
                    ProcessBuilder pb = (ProcessBuilder) target;
                    String command = String.join(" ", pb.command());
                    event.setCommand(command);
                    logger.debug("ProcessBuilder execution detected: {}", command);
                }
            }
            
        } catch (Exception e) {
            logger.error("Error capturing command execution information", e);
        }

        logger.debug("[CommandExecutionHook] Returning event: {}", event);
        logger.debug("Event command: {}", event.getCommand());

        return event;
    }
    
    @Override
    public void onMethodReturn(HookEvent event, Object returnValue) {
        super.onMethodReturn(event, returnValue);
        
        if (event instanceof CommandExecutionEvent) {
            CommandExecutionEvent cmdEvent = (CommandExecutionEvent) event;
            logger.debug("Command execution completed: {}", cmdEvent.getCommand());
        }
    }
    
    @Override
    public void onMethodThrow(HookEvent event, Throwable throwable) {
        super.onMethodThrow(event, throwable);
        
        if (event instanceof CommandExecutionEvent) {
            CommandExecutionEvent cmdEvent = (CommandExecutionEvent) event;
            logger.warn("Command execution failed: {} -> {}", 
                       cmdEvent.getCommand(), throwable.getMessage());
        }
    }
}
