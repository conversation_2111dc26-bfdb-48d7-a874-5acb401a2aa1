# RASP Agent Configuration File
# This file controls the behavior of RASP agent including rule enablement

# ============================================================================
# Global Configuration
# ============================================================================

# Enable/disable RASP agent globally
rasp.enabled=true

# Log level: TRACE, DEBUG, INFO, WARN, ERROR
rasp.log.level=DEBUG

# Log file path (relative to application working directory)
rasp.log.file=logs/rasp-agent.log

# ============================================================================
# Rule Configuration
# ============================================================================

# HTTP Request Logging Rule
rasp.rule.HttpRequestLogRule.enabled=true

# Command Execution Security Rule
rasp.rule.CommandExecutionRule.enabled=true

# File Write Monitoring Rule
rasp.rule.FileWriteRule.enabled=true

# Method Call Logging Rule - Enable for testing DEBUG logs
rasp.rule.MethodCallLogRule.enabled=true
