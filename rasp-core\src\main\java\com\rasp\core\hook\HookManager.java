package com.rasp.core.hook;

import com.rasp.api.event.HookEvent;
import com.rasp.api.hook.Hook;
import com.rasp.core.classloader.ModuleClassLoader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * Hook管理器
 * 负责Hook的加载、卸载、管理和调用
 */
public class HookManager {
    
    private static final Logger logger = LoggerFactory.getLogger(HookManager.class);
    
    /**
     * RASP内部包名列表，这些包下的类不应该被Hook
     */
    private static final String[] RASP_INTERNAL_PACKAGES = {
        "com.rasp.",           // RASP所有内部包
        "ch.qos.logback.",     // logback日志框架
        "org.slf4j.",          // SLF4J日志接口
        "java.util.logging.",  // JDK日志
        "org.apache.logging.", // Apache日志框架
        "sun.",                // JDK内部包
        "com.sun.",            // JDK内部包
        "jdk.internal.",       // JDK内部包
        "java.lang.instrument.", // 字节码增强相关
        "java.lang.reflect."   // 反射相关
    };
    
    /**
     * 线程本地变量，防止递归Hook
     */
    private static final ThreadLocal<Boolean> isProcessing = new ThreadLocal<Boolean>() {
        @Override
        protected Boolean initialValue() {
            return false;
        }
    };
    
    /**
     * 单例实例
     */
    private static volatile HookManager instance;
    
    /**
     * 已注册的Hook列表
     */
    private final List<Hook> hooks = new CopyOnWriteArrayList<>();
    
    /**
     * Hook名称到Hook实例的映射
     */
    private final Map<String, Hook> hookMap = new ConcurrentHashMap<>();
    
    /**
     * Hook类加载器映射
     */
    private final Map<String, ModuleClassLoader> hookClassLoaders = new ConcurrentHashMap<>();
    
    /**
     * 类名到Hook列表的映射，用于快速查找
     */
    private final Map<String, List<Hook>> classHookMap = new ConcurrentHashMap<>();
    
    private HookManager() {
    }
    
    /**
     * 检查是否为RASP内部类
     * @param className 类名
     * @return true表示是内部类，不应该被Hook
     */
    private static boolean isRaspInternalClass(String className) {
        if (className == null) {
            return false;
        }
        
        // 检查是否匹配任何内部包名
        for (String internalPackage : RASP_INTERNAL_PACKAGES) {
            if (className.startsWith(internalPackage)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 获取单例实例
     * @return HookManager实例
     */
    public static HookManager getInstance() {
        if (instance == null) {
            synchronized (HookManager.class) {
                if (instance == null) {
                    instance = new HookManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * 注册Hook
     * @param hook Hook实例
     */
    public void registerHook(Hook hook) {
        if (hook == null) {
            throw new IllegalArgumentException("Hook cannot be null");
        }
        
        String hookName = hook.getName();
        if (hookMap.containsKey(hookName)) {
            logger.warn("Hook already registered: {}", hookName);
            return;
        }
        
        hooks.add(hook);
        hookMap.put(hookName, hook);
        
        // 更新类名到Hook的映射
        updateClassHookMapping(hook);
        
        logger.info("Hook registered: {} - {}", hookName, hook.getDescription());
    }
    
    /**
     * 卸载Hook
     * @param hookName Hook名称
     */
    public void unregisterHook(String hookName) {
        Hook hook = hookMap.remove(hookName);
        if (hook != null) {
            hooks.remove(hook);
            
            // 更新类名到Hook的映射
            removeFromClassHookMapping(hook);
            
            // 关闭对应的类加载器
            ModuleClassLoader classLoader = hookClassLoaders.remove(hookName);
            if (classLoader != null) {
                try {
                    classLoader.close();
                } catch (Exception e) {
                    logger.error("Error closing class loader for hook: " + hookName, e);
                }
            }
            
            logger.info("Hook unregistered: {}", hookName);
        }
    }
    
    /**
     * 从JAR文件加载Hook
     * @param jarFile JAR文件
     * @param hookClassName Hook类名
     */
    public void loadHookFromJar(File jarFile, String hookClassName) {
        try {
            String moduleName = "hook-" + jarFile.getName();
            ModuleClassLoader classLoader = ModuleClassLoader.fromJarFile(
                moduleName, "1.0.0", jarFile, getClass().getClassLoader());
            
            Class<?> hookClass = classLoader.loadClass(hookClassName);
            if (!Hook.class.isAssignableFrom(hookClass)) {
                throw new IllegalArgumentException("Class is not a Hook: " + hookClassName);
            }
            
            Hook hook = (Hook) hookClass.newInstance();
            registerHook(hook);
            
            hookClassLoaders.put(hook.getName(), classLoader);
            
            logger.info("Hook loaded from jar: {} -> {}", jarFile.getName(), hook.getName());
            
        } catch (Exception e) {
            logger.error("Error loading hook from jar: " + jarFile.getAbsolutePath(), e);
            throw new RuntimeException("Failed to load hook from jar", e);
        }
    }
    
    /**
     * 获取指定类的所有Hook
     * @param className 类名
     * @return Hook列表
     */
    public List<Hook> getHooksForClass(String className) {
        List<Hook> result = new ArrayList<>();
        
        // 直接匹配 - 现在也检查enabled状态
        List<Hook> directHooks = classHookMap.get(className);
        if (directHooks != null) {
            for (Hook hook : directHooks) {
                if (hook.isEnabled()) {  // 添加enabled状态检查
                    result.add(hook);
                }
            }
        }
        
        // 模式匹配
        for (Hook hook : hooks) {
            if (hook.isEnabled() && hook.shouldHookClass(className)) {
                if (!result.contains(hook)) {
                    result.add(hook);
                }
            }
        }
        
        return result;
    }
    
    /**
     * 获取指定方法的所有Hook
     * @param className 类名
     * @param methodName 方法名
     * @param methodSignature 方法签名
     * @return Hook列表
     */
    public List<Hook> getHooksForMethod(String className, String methodName, String methodSignature) {
        List<Hook> classHooks = getHooksForClass(className);
        List<Hook> result = new ArrayList<>();
        
        for (Hook hook : classHooks) {
            if (hook.shouldHookMethod(className, methodName, methodSignature)) {
                result.add(hook);
            }
        }
        
        return result;
    }
    
    /**
     * 调用方法进入时的Hook
     * @param className 类名
     * @param methodName 方法名
     * @param methodSignature 方法签名
     * @param target 目标对象
     * @param arguments 方法参数
     * @return Hook事件列表
     */
    public List<HookEvent> onMethodEnter(String className, String methodName, String methodSignature,
                                        Object target, Object[] arguments) {
        
        // 防止递归Hook
        if (isProcessing.get()) {
            return new ArrayList<>();
        }
        
        // 检查是否为RASP内部类，如果是则直接跳过
        if (isRaspInternalClass(className)) {
            return new ArrayList<>();
        }
        
        try {
            isProcessing.set(true);
            
            List<Hook> hooks = getHooksForMethod(className, methodName, methodSignature);
            List<HookEvent> events = new ArrayList<>();

            for (Hook hook : hooks) {
                try {
                    HookEvent event = hook.onMethodEnter(className, methodName, methodSignature, target, arguments);
                    if (event != null) {
                        events.add(event);

                        // 将事件传递给规则引擎进行处理
                        try {
                            com.rasp.core.rule.RuleEngine ruleEngine = com.rasp.core.rule.RuleEngine.getInstance();
                            java.util.List<com.rasp.api.rule.RuleResult> results = ruleEngine.processEvent(event);

                            // 处理规则结果
                            for (com.rasp.api.rule.RuleResult result : results) {
                                logger.debug("Rule processed event: {} -> {} ({})",
                                           result.getRuleName(), result.getAction(), result.getMessage());

                                // 如果规则要求阻断，输出安全告警
                                if (result.getAction() == com.rasp.api.rule.RuleResult.Action.BLOCK) {
                                    logger.warn("SECURITY ALERT: {} - {}", result.getRuleName(), result.getMessage());
                                }
                            }

                        } catch (Exception ruleException) {
                            logger.error("Error processing event with rule engine", ruleException);
                        }
                    }
                } catch (Exception e) {
                    logger.error("Error in hook onMethodEnter: " + hook.getName(), e);
                }
            }

            return events;
            
        } finally {
            isProcessing.set(false);
        }
    }
    
    /**
     * 调用方法返回时的Hook
     * @param events Hook事件列表
     * @param returnValue 返回值
     */
    public void onMethodReturn(List<HookEvent> events, Object returnValue) {
        if (events == null || events.isEmpty()) {
            return;
        }
        
        for (HookEvent event : events) {
            try {
                Hook hook = findHookByEvent(event);
                if (hook != null) {
                    hook.onMethodReturn(event, returnValue);
                }
            } catch (Exception e) {
                logger.error("Error in hook onMethodReturn", e);
            }
        }
    }
    
    /**
     * 调用方法抛出异常时的Hook
     * @param events Hook事件列表
     * @param throwable 异常
     */
    public void onMethodThrow(List<HookEvent> events, Throwable throwable) {
        if (events == null || events.isEmpty()) {
            return;
        }
        
        for (HookEvent event : events) {
            try {
                Hook hook = findHookByEvent(event);
                if (hook != null) {
                    hook.onMethodThrow(event, throwable);
                }
            } catch (Exception e) {
                logger.error("Error in hook onMethodThrow", e);
            }
        }
    }
    
    /**
     * 根据事件查找对应的Hook
     * @param event Hook事件
     * @return Hook实例
     */
    private Hook findHookByEvent(HookEvent event) {
        // 这里可以通过事件的属性来查找对应的Hook
        // 简单实现：通过类名和方法名匹配
        String className = event.getClassName();
        String methodName = event.getMethodName();
        String methodSignature = event.getMethodSignature();
        
        List<Hook> hooks = getHooksForMethod(className, methodName, methodSignature);
        return hooks.isEmpty() ? null : hooks.get(0);
    }
    
    /**
     * 更新类名到Hook的映射
     * @param hook Hook实例
     */
    private void updateClassHookMapping(Hook hook) {
        String[] classPatterns = hook.getClassNamePatterns();
        if (classPatterns != null) {
            for (String pattern : classPatterns) {
                // 如果是精确匹配（不包含通配符），直接添加到映射中
                if (!pattern.contains("*") && !pattern.contains("?")) {
                    classHookMap.computeIfAbsent(pattern, k -> new ArrayList<>()).add(hook);
                }
            }
        }
    }
    
    /**
     * 从类名到Hook的映射中移除Hook
     * @param hook Hook实例
     */
    private void removeFromClassHookMapping(Hook hook) {
        Iterator<Map.Entry<String, List<Hook>>> iterator = classHookMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, List<Hook>> entry = iterator.next();
            List<Hook> hookList = entry.getValue();
            hookList.remove(hook);
            if (hookList.isEmpty()) {
                iterator.remove();
            }
        }
    }
    
    /**
     * 获取所有已注册的Hook
     * @return Hook列表
     */
    public List<Hook> getAllHooks() {
        return new ArrayList<>(hooks);
    }
    
    /**
     * 根据名称获取Hook
     * @param name Hook名称
     * @return Hook实例，如果不存在返回null
     */
    public Hook getHook(String name) {
        return hookMap.get(name);
    }
    
    /**
     * 清理所有Hook
     */
    public void clear() {
        hooks.clear();
        hookMap.clear();
        classHookMap.clear();
        
        // 关闭所有类加载器
        for (ModuleClassLoader classLoader : hookClassLoaders.values()) {
            try {
                classLoader.close();
            } catch (Exception e) {
                logger.error("Error closing class loader", e);
            }
        }
        hookClassLoaders.clear();
        
        logger.info("All hooks cleared");
    }
}
