package com.rasp.rules.file;

import com.rasp.api.event.HookEvent;
import com.rasp.api.event.FileReadEvent;
import com.rasp.api.event.HttpRequestEvent;
import com.rasp.api.rule.AbstractRule;
import com.rasp.api.rule.RuleResult;
import com.rasp.api.context.RequestContext;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 任意文件读取防护规则
 * 专门防护任意文件读取攻击，包括路径穿越、敏感文件访问等
 */
public class FileReadRule extends AbstractRule {
    
    // 敏感文件列表
    private static final List<String> SENSITIVE_FILES = Arrays.asList(
        // Linux敏感文件
        "/etc/passwd", "/etc/shadow", "/etc/hosts", "/etc/hostname", "/etc/issue",
        "/etc/group", "/etc/sudoers", "/etc/ssh/sshd_config", "/etc/mysql/my.cnf",
        "/etc/apache2/apache2.conf", "/etc/nginx/nginx.conf", "/etc/crontab",
        "/root/.bash_history", "/root/.ssh/id_rsa", "/root/.ssh/authorized_keys",
        "/home/<USER>/.bash_history", "/home/<USER>/.ssh/id_rsa", "/var/log/auth.log",
        "/var/log/apache2/access.log", "/var/log/nginx/access.log",
        
        // Windows敏感文件
        "C:\\Windows\\System32\\config\\SAM", "C:\\Windows\\System32\\config\\SYSTEM",
        "C:\\Windows\\System32\\drivers\\etc\\hosts", "C:\\Windows\\win.ini",
        "C:\\Windows\\system.ini", "C:\\boot.ini", "C:\\autoexec.bat",
        
        // 应用配置文件
        "application.properties", "application.yml", "application.yaml",
        "database.properties", "jdbc.properties", "hibernate.cfg.xml",
        "web.xml", "context.xml", "server.xml", "tomcat-users.xml",
        ".env", "config.php", "wp-config.php", "settings.py"
    );
    
    // 敏感目录列表
    private static final List<String> SENSITIVE_DIRECTORIES = Arrays.asList(
        // Linux敏感目录
        "/etc", "/root", "/home", "/var/log", "/usr/bin", "/bin", "/sbin",
        "/proc", "/sys", "/dev", "/boot",
        
        // Windows敏感目录
        "C:\\Windows", "C:\\System32", "C:\\Program Files",
        
        // 应用敏感目录
        "/WEB-INF", "/META-INF", "WEB-INF", "META-INF",
        ".git", ".svn", ".hg", "node_modules"
    );
    
    // 敏感文件扩展名
    private static final List<String> SENSITIVE_EXTENSIONS = Arrays.asList(
        // 配置文件
        "conf", "config", "cfg", "ini", "properties", "yml", "yaml", "xml",
        "env", "key", "pem", "crt", "cer", "p12", "jks", "keystore",
        
        // 日志文件
        "log", "logs", "out", "err",
        
        // 数据库文件
        "db", "sqlite", "sqlite3", "mdb", "accdb",
        
        // 备份文件
        "bak", "backup", "old", "orig", "tmp", "temp",
        
        // 源码文件（可能包含敏感信息）
        "java", "class", "jsp", "php", "asp", "aspx", "py", "rb", "go"
    );
    
    // 路径穿越模式
    private static final Pattern PATH_TRAVERSAL_PATTERN = Pattern.compile(
        "(\\.\\./|\\.\\\\|%2e%2e%2f|%2e%2e%5c|\\.\\.%2f|\\.\\.%5c|\\.\\.\\\\)", 
        Pattern.CASE_INSENSITIVE
    );
    
    // 敏感关键词模式
    private static final Pattern SENSITIVE_KEYWORDS_PATTERN = Pattern.compile(
        "(passwd|shadow|hosts|config|secret|key|password|token|credential|private)",
        Pattern.CASE_INSENSITIVE
    );
    
    @Override
    public String getName() {
        return "FileReadRule";
    }
    
    @Override
    public String getDescription() {
        return "Advanced rule to detect and prevent arbitrary file read attacks";
    }
    
    @Override
    public int getPriority() {
        return 10; // 高优先级
    }

    @Override
    public HookEvent.EventType[] getSupportedEventTypes() {
        return new HookEvent.EventType[]{HookEvent.EventType.FILE_READ};
    }

    @Override
    protected RuleResult doProcess(HookEvent event) {
        FileReadEvent fileEvent = (FileReadEvent) event;
        String filePath = fileEvent.getFilePath();

        // 详细日志记录
        logger.debug("[FileReadRule] ===== RULE PROCESSING STARTED =====");
        logger.debug("Event Type: {}", event.getEventType());
        logger.debug("Class: {}", fileEvent.getClassName());
        logger.debug("Method: {}", fileEvent.getMethodName());
        logger.debug("File Path: {}", filePath != null ? filePath : "NULL");
        logger.debug("Absolute Path: {}", fileEvent.getAbsolutePath());
        logger.debug("Canonical Path: {}", fileEvent.getCanonicalPath());
        logger.debug("Is Absolute: {}", fileEvent.isAbsolutePath());
        logger.debug("File Exists: {}", fileEvent.isFileExists());
        logger.debug("File Extension: {}", fileEvent.getFileExtension());
        logger.debug("Parent Directory: {}", fileEvent.getParentDirectory());
        logger.debug("Timestamp: {}", new java.util.Date());

        if (filePath == null || filePath.trim().isEmpty()) {
            logger.debug("[FileReadRule] Empty or null file path detected");
            return RuleResult.allow(getName());
        }

        logger.debug("[FileReadRule] Analyzing file read operation: {}", filePath);

        // 获取HTTP请求上下文
        HttpRequestEvent httpRequest = RequestContext.getHttpRequestEvent();
        boolean hasHttpContext = httpRequest != null;
        
        if (hasHttpContext) {
            logger.debug("[FileReadRule] HTTP Request Context Found!");
            printHttpRequestInfo(httpRequest);
        }

        // === 核心安全检查 ===
        
        // 1. 检查路径穿越攻击 - 最高优先级
        RuleResult pathTraversalResult = checkPathTraversal(fileEvent, httpRequest);
        if (pathTraversalResult.getAction() != RuleResult.Action.ALLOW) {
            return pathTraversalResult;
        }
        
        // 2. 检查敏感文件访问
        RuleResult sensitiveFileResult = checkSensitiveFiles(fileEvent, httpRequest);
        if (sensitiveFileResult.getAction() != RuleResult.Action.ALLOW) {
            return sensitiveFileResult;
        }
        
        // 3. 检查敏感目录访问
        RuleResult sensitiveDirectoryResult = checkSensitiveDirectories(fileEvent, httpRequest);
        if (sensitiveDirectoryResult.getAction() != RuleResult.Action.ALLOW) {
            return sensitiveDirectoryResult;
        }
        
        // 4. 检查敏感文件扩展名
        RuleResult sensitiveExtensionResult = checkSensitiveExtensions(fileEvent, httpRequest);
        if (sensitiveExtensionResult.getAction() != RuleResult.Action.ALLOW) {
            return sensitiveExtensionResult;
        }
        
        // 5. 检查用户可控的绝对路径读取
        RuleResult controllablePathResult = checkControllableAbsolutePath(fileEvent, httpRequest);
        if (controllablePathResult.getAction() != RuleResult.Action.ALLOW) {
            return controllablePathResult;
        }
        
        // 6. 检查敏感关键词
        RuleResult keywordResult = checkSensitiveKeywords(fileEvent, httpRequest);
        if (keywordResult.getAction() != RuleResult.Action.ALLOW) {
            return keywordResult;
        }

        logger.debug("[FileReadRule] File read operation passed all security checks");
        logger.debug("[FileReadRule] ===== RULE PROCESSING COMPLETED =====");

        return RuleResult.log(getName(), RuleResult.RiskLevel.LOW,
                            "File read operation monitored and logged: " + filePath);
    }
    
    /**
     * 检查路径穿越攻击
     */
    private RuleResult checkPathTraversal(FileReadEvent fileEvent, HttpRequestEvent httpRequest) {
        String filePath = fileEvent.getFilePath();
        String canonicalPath = fileEvent.getCanonicalPath();
        
        // 检查文件路径中的路径穿越字符
        if (PATH_TRAVERSAL_PATTERN.matcher(filePath).find()) {
            String message = String.format("Path traversal attack detected in file read: %s", filePath);
            logger.warn("[SECURITY ALERT] {}", message);
            return RuleResult.block(getName(), RuleResult.RiskLevel.CRITICAL, message);
        }
        
        // 检查规范路径与原始路径的差异（可能的路径穿越）
        if (canonicalPath != null && !canonicalPath.equals(fileEvent.getAbsolutePath())) {
            String message = String.format("Potential path traversal detected: original=%s, canonical=%s", 
                fileEvent.getAbsolutePath(), canonicalPath);
            logger.warn("[SECURITY ALERT] {}", message);
            return RuleResult.alert(getName(), RuleResult.RiskLevel.HIGH, message);
        }
        
        // 检查HTTP请求参数中的路径穿越
        if (httpRequest != null) {
            String queryString = httpRequest.getQueryString();
            if (queryString != null && PATH_TRAVERSAL_PATTERN.matcher(queryString).find()) {
                String message = String.format("Path traversal in query string for file read: %s", queryString);
                logger.warn("[SECURITY ALERT] {}", message);
                return RuleResult.block(getName(), RuleResult.RiskLevel.CRITICAL, message);
            }
        }
        
        return RuleResult.allow(getName());
    }
    
    /**
     * 检查敏感文件访问
     */
    private RuleResult checkSensitiveFiles(FileReadEvent fileEvent, HttpRequestEvent httpRequest) {
        String filePath = fileEvent.getFilePath();
        String absolutePath = fileEvent.getAbsolutePath();
        String canonicalPath = fileEvent.getCanonicalPath();
        
        // 检查所有路径变体
        String[] pathsToCheck = {filePath, absolutePath, canonicalPath};
        
        for (String path : pathsToCheck) {
            if (path == null) continue;
            
            String lowerPath = path.toLowerCase();
            
            for (String sensitiveFile : SENSITIVE_FILES) {
                String lowerSensitiveFile = sensitiveFile.toLowerCase();
                
                // 精确匹配或路径结尾匹配
                if (lowerPath.equals(lowerSensitiveFile) || lowerPath.endsWith(lowerSensitiveFile)) {
                    String message = String.format("Sensitive file access detected: %s (matched: %s)", 
                        path, sensitiveFile);
                    logger.warn("[SECURITY ALERT] {}", message);
                    
                    // 如果有HTTP上下文，说明可能是攻击
                    if (httpRequest != null) {
                        return RuleResult.block(getName(), RuleResult.RiskLevel.CRITICAL, message);
                    } else {
                        return RuleResult.alert(getName(), RuleResult.RiskLevel.HIGH, message);
                    }
                }
            }
        }
        
        return RuleResult.allow(getName());
    }

    /**
     * 检查敏感目录访问
     */
    private RuleResult checkSensitiveDirectories(FileReadEvent fileEvent, HttpRequestEvent httpRequest) {
        String absolutePath = fileEvent.getAbsolutePath();
        if (absolutePath == null) {
            return RuleResult.allow(getName());
        }

        String lowerPath = absolutePath.toLowerCase();

        for (String sensitiveDir : SENSITIVE_DIRECTORIES) {
            String lowerSensitiveDir = sensitiveDir.toLowerCase();
            if (lowerPath.startsWith(lowerSensitiveDir)) {
                String message = String.format("Sensitive directory access detected: %s in '%s'",
                    sensitiveDir, absolutePath);
                logger.warn("[SECURITY ALERT] {}", message);

                // 如果有HTTP上下文，说明可能是攻击
                if (httpRequest != null) {
                    return RuleResult.block(getName(), RuleResult.RiskLevel.HIGH, message);
                } else {
                    return RuleResult.alert(getName(), RuleResult.RiskLevel.MEDIUM, message);
                }
            }
        }

        return RuleResult.allow(getName());
    }

    /**
     * 检查敏感文件扩展名
     */
    private RuleResult checkSensitiveExtensions(FileReadEvent fileEvent, HttpRequestEvent httpRequest) {
        String extension = fileEvent.getFileExtension();
        if (extension == null) {
            return RuleResult.allow(getName());
        }

        String lowerExtension = extension.toLowerCase();

        if (SENSITIVE_EXTENSIONS.contains(lowerExtension)) {
            String message = String.format("Sensitive file type access: .%s in '%s'",
                extension, fileEvent.getFilePath());
            logger.debug("[SECURITY ALERT] {}", message);

            // 配置文件和密钥文件高风险
            if (Arrays.asList("key", "pem", "crt", "cer", "p12", "jks", "keystore").contains(lowerExtension)) {
                if (httpRequest != null) {
                    return RuleResult.block(getName(), RuleResult.RiskLevel.HIGH,
                        "Cryptographic key file access: " + message);
                } else {
                    return RuleResult.alert(getName(), RuleResult.RiskLevel.HIGH, message);
                }
            }
            // 配置文件中等风险
            else if (Arrays.asList("conf", "config", "cfg", "ini", "properties", "yml", "yaml", "env").contains(lowerExtension)) {
                return RuleResult.alert(getName(), RuleResult.RiskLevel.MEDIUM, message);
            }
            // 其他敏感文件低风险告警
            else {
                return RuleResult.alert(getName(), RuleResult.RiskLevel.LOW, message);
            }
        }

        return RuleResult.allow(getName());
    }

    /**
     * 检查用户可控的绝对路径读取
     */
    private RuleResult checkControllableAbsolutePath(FileReadEvent fileEvent, HttpRequestEvent httpRequest) {
        // 如果不是绝对路径，直接允许
        if (!fileEvent.isAbsolutePath()) {
            return RuleResult.allow(getName());
        }

        // 如果没有HTTP请求上下文，说明不是来自Web请求，允许
        if (httpRequest == null) {
            logger.debug("[FileReadRule] Absolute path read without HTTP context (likely internal operation): {}",
                fileEvent.getAbsolutePath());
            return RuleResult.allow(getName());
        }

        String absolutePath = fileEvent.getAbsolutePath();
        String filePath = fileEvent.getFilePath();

        // 检查绝对路径或文件路径是否来自HTTP请求
        boolean pathFromRequest = checkFilePathFromHttpRequest(absolutePath, httpRequest) ||
                                 checkFilePathFromHttpRequest(filePath, httpRequest);

        if (pathFromRequest) {
            logger.warn("[FileReadRule] USER-CONTROLLABLE ABSOLUTE PATH READ DETECTED!");
            String message = String.format(
                "User-controllable absolute path file read detected: %s (from HTTP request: %s %s)",
                absolutePath,
                httpRequest.getRequestMethod(),
                httpRequest.getRequestUrl()
            );

            // 用户可控的绝对路径读取是高风险行为
            return RuleResult.alert(getName(), RuleResult.RiskLevel.HIGH, message);
        } else {
            // 绝对路径读取但不是用户可控的，记录但不告警
            logger.debug("[FileReadRule] Absolute path read (not user-controllable): {}", absolutePath);
            return RuleResult.allow(getName());
        }
    }

    /**
     * 检查敏感关键词
     */
    private RuleResult checkSensitiveKeywords(FileReadEvent fileEvent, HttpRequestEvent httpRequest) {
        String filePath = fileEvent.getFilePath();

        if (filePath != null && SENSITIVE_KEYWORDS_PATTERN.matcher(filePath).find()) {
            String message = String.format("Sensitive keyword detected in file path: %s", filePath);
            logger.debug("[SECURITY ALERT] {}", message);

            // 如果有HTTP上下文，风险更高
            if (httpRequest != null) {
                return RuleResult.alert(getName(), RuleResult.RiskLevel.MEDIUM, message);
            } else {
                return RuleResult.alert(getName(), RuleResult.RiskLevel.LOW, message);
            }
        }

        return RuleResult.allow(getName());
    }

    /**
     * 打印HTTP请求信息
     */
    private void printHttpRequestInfo(HttpRequestEvent httpRequest) {
        logger.debug("=================== HTTP REQUEST ANALYSIS ===================");
        logger.debug("Request URL: " + httpRequest.getRequestUrl());
        logger.debug("Request Method: " + httpRequest.getRequestMethod());
        logger.debug("Request Path: " + httpRequest.getRequestPath());
        logger.debug("Query String: " + httpRequest.getQueryString());
        logger.debug("Client IP: " + httpRequest.getClientIp());
        logger.debug("User-Agent: " + httpRequest.getUserAgent());
        logger.debug("Session ID: " + httpRequest.getSessionId());

        // 打印请求参数
        if (httpRequest.getRequestParameters() != null && !httpRequest.getRequestParameters().isEmpty()) {
            logger.debug("Request Parameters:");
            for (java.util.Map.Entry<String, String[]> entry : httpRequest.getRequestParameters().entrySet()) {
                String paramName = entry.getKey();
                String[] paramValues = entry.getValue();
                if (paramValues != null) {
                    for (String value : paramValues) {
                        logger.debug("    " + paramName + " = " + value);

                        // 检查参数中的路径穿越特征
                        if (value != null && PATH_TRAVERSAL_PATTERN.matcher(value).find()) {
                            logger.debug("    >>> PATH TRAVERSAL DETECTED: " + value);
                        }

                        // 检查参数中的敏感关键词
                        if (value != null && SENSITIVE_KEYWORDS_PATTERN.matcher(value).find()) {
                            logger.debug("    >>> SENSITIVE KEYWORD DETECTED: " + value);
                        }
                    }
                }
            }
        }

        // 打印请求体
        if (httpRequest.getRequestBody() != null && !httpRequest.getRequestBody().isEmpty()) {
            logger.debug("Request Body: " + httpRequest.getRequestBody());

            String body = httpRequest.getRequestBody();
            if (PATH_TRAVERSAL_PATTERN.matcher(body).find()) {
                logger.debug(">>> PATH TRAVERSAL IN BODY DETECTED");
            }
            if (SENSITIVE_KEYWORDS_PATTERN.matcher(body).find()) {
                logger.debug(">>> SENSITIVE KEYWORD IN BODY DETECTED");
            }
        }

        logger.debug("=================== END OF HTTP ANALYSIS ===================");
    }

    /**
     * 检查文件路径是否来自HTTP请求参数
     */
    private boolean checkFilePathFromHttpRequest(String filePath, HttpRequestEvent httpRequest) {
        if (filePath == null || httpRequest == null) {
            return false;
        }

        // 标准化路径用于比较
        String normalizedPath = normalizePath(filePath);

        // 检查查询字符串
        if (checkPathInString(filePath, normalizedPath, httpRequest.getQueryString(), "query string")) {
            return true;
        }

        // 检查请求参数
        if (httpRequest.getRequestParameters() != null) {
            for (java.util.Map.Entry<String, String[]> entry : httpRequest.getRequestParameters().entrySet()) {
                String paramName = entry.getKey();
                String[] paramValues = entry.getValue();
                if (paramValues != null) {
                    for (String value : paramValues) {
                        if (value != null) {
                            if (checkPathInString(filePath, normalizedPath, value, "parameter '" + paramName + "'")) {
                                return true;
                            }
                        }
                    }
                }
            }
        }

        // 检查请求体
        if (checkPathInString(filePath, normalizedPath, httpRequest.getRequestBody(), "request body")) {
            return true;
        }

        return false;
    }

    /**
     * 标准化路径，处理不同的路径分隔符和编码
     */
    private String normalizePath(String path) {
        if (path == null) {
            return null;
        }

        // 统一路径分隔符
        String normalized = path.replace('\\', '/');

        // URL解码
        try {
            normalized = java.net.URLDecoder.decode(normalized, "UTF-8");
        } catch (Exception e) {
            // 解码失败，使用原始路径
        }

        return normalized.toLowerCase();
    }

    /**
     * 在字符串中检查路径的各种变体
     */
    private boolean checkPathInString(String originalPath, String normalizedPath, String searchIn, String location) {
        if (searchIn == null) {
            return false;
        }

        String searchInLower = searchIn.toLowerCase();
        String searchInNormalized = normalizePath(searchIn);

        // 1. 精确匹配原始路径
        if (searchIn.equals(originalPath)) {
            logger.debug("File path '{}' exactly matches in {}: {}", originalPath, location, searchIn);
            return true;
        }

        // 2. 包含匹配原始路径
        if (searchIn.contains(originalPath)) {
            logger.debug("File path '{}' found in {}: {}", originalPath, location, searchIn);
            return true;
        }

        // 3. 标准化路径匹配
        if (normalizedPath != null && searchInNormalized != null) {
            if (searchInNormalized.contains(normalizedPath)) {
                logger.debug("Normalized file path '{}' found in {}: {}", normalizedPath, location, searchIn);
                return true;
            }
        }

        // 4. 检查路径的文件名部分
        String fileName = extractFileName(originalPath);
        if (fileName != null && fileName.length() > 3) { // 避免太短的文件名误匹配
            if (searchInLower.contains(fileName.toLowerCase())) {
                logger.debug("File name '{}' from path '{}' found in {}: {}", fileName, originalPath, location, searchIn);
                return true;
            }
        }

        return false;
    }

    /**
     * 提取文件名
     */
    private String extractFileName(String path) {
        if (path == null) {
            return null;
        }

        int lastSlash = Math.max(path.lastIndexOf('/'), path.lastIndexOf('\\'));
        if (lastSlash >= 0 && lastSlash < path.length() - 1) {
            return path.substring(lastSlash + 1);
        }

        return path;
    }
}
