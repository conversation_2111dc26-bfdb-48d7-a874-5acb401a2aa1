# RASP任意文件读取防护指南

## 🎯 概述

基于现有的FileWriteRule，我们构建了一套完整的任意文件读取防护体系，包括FileReadHook、FileReadEvent和FileReadRule，形成了对文件读写操作的全面防护。

## 🏗️ 架构设计

### 防护体系组件

```
┌─────────────────────────────────────────────────────────────┐
│                    文件操作防护体系                          │
├─────────────────────────────────────────────────────────────┤
│  FileReadHook  │  FileWriteHook  │  文件操作拦截层          │
├─────────────────────────────────────────────────────────────┤
│  FileReadEvent │  FileWriteEvent │  事件封装层              │
├─────────────────────────────────────────────────────────────┤
│  FileReadRule  │  FileWriteRule  │  安全规则层              │
└─────────────────────────────────────────────────────────────┘
```

### 新增组件

1. **FileReadEvent** - 文件读取事件
2. **FileReadHook** - 文件读取Hook
3. **FileReadRule** - 任意文件读取防护规则

## 📋 FileReadRule 防护策略

### 🔴 六层安全检查机制

#### 1. **路径穿越攻击检测** (CRITICAL级别)
- 检测 `../`, `.\`, URL编码变体
- 比较规范路径与原始路径差异
- 检查HTTP请求参数中的路径穿越

#### 2. **敏感文件访问检测** (CRITICAL级别)
```java
// 敏感文件列表
"/etc/passwd", "/etc/shadow", "/etc/hosts"
"C:\\Windows\\System32\\config\\SAM"
"application.properties", "web.xml", ".env"
```

#### 3. **敏感目录访问检测** (HIGH级别)
```java
// 敏感目录列表
"/etc", "/root", "/home", "/var/log"
"C:\\Windows", "C:\\System32"
"/WEB-INF", "/META-INF", ".git"
```

#### 4. **敏感文件扩展名检测** (分级处理)
- **HIGH级别**: 密钥文件 (.key, .pem, .crt, .jks)
- **MEDIUM级别**: 配置文件 (.conf, .config, .ini, .yml)
- **LOW级别**: 其他敏感文件 (.log, .db, .bak)

#### 5. **用户可控绝对路径检测** (HIGH级别)
- 只有当绝对路径来自HTTP请求时才告警
- 避免内部操作的误报
- 重点关注用户可控的安全风险

#### 6. **敏感关键词检测** (MEDIUM/LOW级别)
- 检测路径中的敏感关键词
- 模式: `(passwd|shadow|config|secret|key|password|token)`

## 🔍 FileReadHook 拦截范围

### 支持的文件读取类

```java
// 传统IO类
"java.io.FileInputStream"
"java.io.RandomAccessFile" 
"java.io.FileReader"
"java.io.BufferedReader"
"java.util.Scanner"

// NIO类
"java.nio.file.Files"
"java.nio.channels.FileChannel"

// Apache Commons IO
"org.apache.commons.io.FileUtils"
"org.apache.commons.io.IOUtils"

// Spring Framework
"org.springframework.core.io.FileSystemResource"
"org.springframework.core.io.ClassPathResource"

// 其他常见库
"java.util.Properties"
"java.util.zip.ZipFile"
```

### 支持的方法

```java
"read", "readLine", "readAllBytes", "readAllLines"
"readString", "load", "getInputStream", "copy"
"next", "nextLine", "hasNext", "hasNextLine"
```

## 🛡️ 防护特性

### 1. **智能路径检测**
- 支持多种路径格式 (Windows/Linux)
- URL解码和路径标准化
- 文件名、目录名分别匹配

### 2. **HTTP上下文关联**
- 结合HTTP请求进行风险评估
- 区分内部操作和外部攻击
- 详细的请求参数分析

### 3. **分级风险评估**
- **CRITICAL**: 确认的攻击行为，直接阻断
- **HIGH**: 高风险行为，告警并可选阻断
- **MEDIUM**: 中等风险，告警记录
- **LOW**: 低风险，仅记录监控

### 4. **用户可控性判断**
- 检查文件路径是否来自HTTP请求
- 精确匹配、包含匹配、标准化匹配
- 避免内部文件操作的误报

## 📊 配置管理

### 配置文件设置

```properties
# 文件读取防护规则
rasp.rule.FileReadRule.enabled=true

# 文件写入防护规则  
rasp.rule.FileWriteRule.enabled=true
```

### 环境特定配置

#### 开发环境
```properties
# 启用详细监控，便于调试
rasp.rule.FileReadRule.enabled=true
rasp.log.level=DEBUG
```

#### 生产环境
```properties
# 启用安全防护，优化性能
rasp.rule.FileReadRule.enabled=true
rasp.log.level=WARN
```

## 🎯 攻击场景覆盖

### 1. **路径穿越攻击**
```
GET /download?file=../../../etc/passwd
GET /view?path=..\..\Windows\System32\config\SAM
```

### 2. **敏感文件读取**
```
GET /config?file=application.properties
POST /read {"filename": "/etc/shadow"}
```

### 3. **目录遍历攻击**
```
GET /files?dir=/etc/
GET /browse?path=C:\Windows\System32\
```

### 4. **配置文件泄露**
```
GET /backup?file=database.properties
GET /export?config=web.xml
```

## 💡 最佳实践

### 1. **部署建议**
- 生产环境启用FileReadRule和FileWriteRule
- 根据应用特点调整敏感文件列表
- 定期审查告警日志

### 2. **性能优化**
- 合理设置日志级别
- 避免过于宽泛的文件路径匹配
- 定期清理日志文件

### 3. **误报处理**
- 分析内部文件操作模式
- 调整敏感文件和目录列表
- 优化用户可控性判断逻辑

## 🔧 扩展能力

### 1. **自定义敏感文件**
可以根据应用特点扩展敏感文件列表：

```java
// 添加应用特定的敏感文件
"myapp.conf", "secret.key", "private.pem"
```

### 2. **自定义检测逻辑**
可以添加新的检测方法：

```java
private RuleResult checkCustomPatterns(FileReadEvent fileEvent, HttpRequestEvent httpRequest) {
    // 自定义检测逻辑
}
```

### 3. **集成外部威胁情报**
可以集成外部威胁情报来增强检测能力。

## 📈 监控指标

### 关键指标
- 文件读取操作总数
- 敏感文件访问次数
- 路径穿越攻击检测数
- 用户可控路径访问数
- 阻断/告警比例

### 日志分析
- 按文件类型统计访问频率
- 按IP地址分析攻击模式
- 按时间段分析异常行为

## 🚀 总结

通过FileReadRule和FileWriteRule的组合，我们构建了一套完整的文件操作安全防护体系：

✅ **全面覆盖**: 读写操作双重防护  
✅ **智能检测**: 多层安全检查机制  
✅ **精准识别**: 用户可控性判断  
✅ **分级响应**: 风险等级化处理  
✅ **配置灵活**: 支持环境特定配置  
✅ **性能优化**: 避免误报和性能影响  

这套防护体系能够有效防护任意文件读写攻击，为Web应用提供强有力的运行时安全保护！
