# RASP Agent Configuration File - Development Environment
# This configuration is optimized for development and debugging

# ============================================================================
# Global Configuration
# ============================================================================

rasp.enabled=true
rasp.log.level=DEBUG
rasp.log.file=logs/rasp-agent-dev.log

# ============================================================================
# Rule Configuration - Development Mode
# ============================================================================
# Enable more verbose logging and monitoring for development

# HTTP Request Logging Rule - ENABLED for development monitoring
rasp.rule.HttpRequestLogRule.enabled=true

# Command Execution Security Rule - ENABLED for security testing
rasp.rule.CommandExecutionRule.enabled=true

# File Write Monitoring Rule - ENABLED for file operation monitoring
rasp.rule.FileWriteRule.enabled=true

# Method Call Logging Rule - ENABLED for detailed debugging (verbose!)
rasp.rule.MethodCallLogRule.enabled=true

# ============================================================================
# Hook Configuration
# ============================================================================

rasp.hook.http.enabled=true
rasp.hook.command.enabled=true
rasp.hook.file.enabled=true

# ============================================================================
# Performance Configuration - Relaxed for Development
# ============================================================================

rasp.performance.max_events_per_second=2000
rasp.performance.max_rule_execution_time=200
rasp.performance.monitoring.enabled=true

# ============================================================================
# Security Configuration
# ============================================================================

rasp.security.default_action=LOG
rasp.security.alerts.enabled=true
rasp.security.alert_threshold=3

# ============================================================================
# Advanced Configuration - Development Settings
# ============================================================================

rasp.advanced.retransform_loaded_classes=true
rasp.advanced.max_enhance_batch_size=50

# Debug mode enabled for development
rasp.debug.enabled=true
rasp.debug.collect_stack_traces=true

# ============================================================================
# External Module Configuration
# ============================================================================

rasp.external.hooks.directory=hooks
rasp.external.rules.directory=rules
rasp.external.modules.enabled=true
