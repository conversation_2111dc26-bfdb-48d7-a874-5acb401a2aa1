package com.rasp.rules.http;

import com.rasp.api.event.HookEvent;
import com.rasp.api.event.HttpRequestEvent;
import com.rasp.api.rule.AbstractRule;
import com.rasp.api.rule.RuleResult;

import java.util.Map;

/**
 * HTTP请求日志规则
 * 示例规则，用于打印HTTP请求的详细信息
 */
public class HttpRequestLogRule extends AbstractRule {
    
    @Override
    public String getName() {
        return "HttpRequestLogRule";
    }
    
    @Override
    public String getDescription() {
        return "Log HTTP request information for monitoring and analysis";
    }
    
    @Override
    public int getPriority() {
        return 10; // 高优先级，优先执行
    }
    
    @Override
    public HookEvent.EventType[] getSupportedEventTypes() {
        return new HookEvent.EventType[]{
            HookEvent.EventType.HTTP_REQUEST
        };
    }
    
    @Override
    protected RuleResult doProcess(HookEvent event) {
        if (!(event instanceof HttpRequestEvent)) {
            return RuleResult.allow(getName());
        }
        
        HttpRequestEvent httpEvent = (HttpRequestEvent) event;
//
//        // 打印HTTP请求的详细信息
//        System.out.println("=== HTTP Request Information ===");
//        System.out.println("Timestamp: " + httpEvent.getTimestamp());
//        System.out.println("Thread: " + httpEvent.getThreadName() + " (ID: " + httpEvent.getThreadId() + ")");
//        System.out.println("Class: " + httpEvent.getClassName());
//        System.out.println("Method: " + httpEvent.getMethodName());
//        System.out.println("Method Signature: " + httpEvent.getMethodSignature());
//
//        // HTTP请求基本信息
//        System.out.println("--- Request Basic Info ---");
//        System.out.println("URL: " + httpEvent.getRequestUrl());
//        System.out.println("Method: " + httpEvent.getRequestMethod());
//        System.out.println("Path: " + httpEvent.getRequestPath());
//        System.out.println("Query String: " + httpEvent.getQueryString());
//        System.out.println("Protocol: " + httpEvent.getProtocol());
//        System.out.println("Server: " + httpEvent.getServerName() + ":" + httpEvent.getServerPort());
//
//        // 客户端信息
//        System.out.println("--- Client Info ---");
//        System.out.println("Client IP: " + httpEvent.getClientIp());
//        System.out.println("User-Agent: " + httpEvent.getUserAgent());
//        System.out.println("Referer: " + httpEvent.getReferer());
//        System.out.println("Session ID: " + httpEvent.getSessionId());
//
//        // 请求头信息
//        System.out.println("--- Request Headers ---");
//        Map<String, String> headers = httpEvent.getRequestHeaders();
//        if (headers != null && !headers.isEmpty()) {
//            for (Map.Entry<String, String> entry : headers.entrySet()) {
//                System.out.println(entry.getKey() + ": " + entry.getValue());
//            }
//        } else {
//            System.out.println("No headers captured");
//        }
//
//        // 请求参数
//        System.out.println("--- Request Parameters ---");
//        Map<String, String[]> parameters = httpEvent.getRequestParameters();
//        if (parameters != null && !parameters.isEmpty()) {
//            for (Map.Entry<String, String[]> entry : parameters.entrySet()) {
//                String key = entry.getKey();
//                String[] values = entry.getValue();
//                if (values != null && values.length > 0) {
//                    if (values.length == 1) {
//                        System.out.println(key + ": " + values[0]);
//                    } else {
//                        System.out.println(key + ": " + java.util.Arrays.toString(values));
//                    }
//                }
//            }
//        } else {
//            System.out.println("No parameters");
//        }
//
//        // 请求体
//        System.out.println("--- Request Body ---");
//        String requestBody = httpEvent.getRequestBody();
//        if (requestBody != null && !requestBody.isEmpty()) {
//            System.out.println(requestBody);
//        } else {
//            System.out.println("No request body or not captured");
//        }
//
//        // 方法参数信息
//        System.out.println("--- Method Arguments ---");
//        Object[] arguments = httpEvent.getArguments();
//        if (arguments != null && arguments.length > 0) {
//            System.out.println("Arguments: " + formatArguments(arguments));
//        } else {
//            System.out.println("No method arguments");
//        }
//
//        // 堆栈信息（只显示前几层）
//        System.out.println("--- Stack Trace (Top 5) ---");
//        StackTraceElement[] stackTrace = httpEvent.getStackTrace();
//        if (stackTrace != null && stackTrace.length > 0) {
//            int maxLines = Math.min(5, stackTrace.length);
//            for (int i = 0; i < maxLines; i++) {
//                System.out.println("\tat " + stackTrace[i]);
//            }
//            if (stackTrace.length > maxLines) {
//                System.out.println("\t... and " + (stackTrace.length - maxLines) + " more");
//            }
//        }
//
//        System.out.println("=== End of HTTP Request Info ===\n");
//
        // 记录日志并允许继续执行
        return RuleResult.log(getName(), RuleResult.RiskLevel.LOW, 
                             "HTTP request logged: " + httpEvent.getRequestMethod() + " " + httpEvent.getRequestUrl());
    }
}
