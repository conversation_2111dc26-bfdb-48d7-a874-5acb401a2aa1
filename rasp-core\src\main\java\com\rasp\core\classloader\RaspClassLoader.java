package com.rasp.core.classloader;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLClassLoader;
import java.util.Enumeration;
import java.util.concurrent.ConcurrentHashMap;

/**
 * RASP自定义类加载器
 * 参考jvm-sandbox的类隔离机制，破坏双亲委派模型
 * 实现RASP与目标应用的类隔离
 */
public class RaspClassLoader extends URLClassLoader {
    
    private static final Logger logger = LoggerFactory.getLogger(RaspClassLoader.class);
    
    /**
     * 需要委派给父类加载器的类前缀
     */
    private static final String[] PARENT_DELEGATE_PREFIXES = {
        "java.",
        "javax.",
        "sun.",
        "com.sun.",
        "org.xml.",
        "org.w3c.",
        "org.slf4j.",
        "ch.qos.logback."
    };
    
    /**
     * RASP核心包前缀，需要由RASP类加载器加载
     */
    private static final String[] RASP_CORE_PREFIXES = {
        "com.rasp.core.",
        "com.rasp.api."
    };
    
    /**
     * 类缓存
     */
    private final ConcurrentHashMap<String, Class<?>> classCache = new ConcurrentHashMap<>();
    
    /**
     * 命名空间，用于区分不同的RASP实例
     */
    private final String namespace;
    
    public RaspClassLoader(URL[] urls, ClassLoader parent, String namespace) {
        super(urls, parent);
        this.namespace = namespace;
        logger.info("RaspClassLoader created with namespace: {}", namespace);
    }
    
    @Override
    protected Class<?> loadClass(String name, boolean resolve) throws ClassNotFoundException {
        synchronized (getClassLoadingLock(name)) {
            // 首先检查类是否已经被加载
            Class<?> clazz = classCache.get(name);
            if (clazz != null) {
                if (resolve) {
                    resolveClass(clazz);
                }
                return clazz;
            }
            
            // 检查是否需要委派给父类加载器
            if (shouldDelegateToParent(name)) {
                try {
                    clazz = getParent().loadClass(name);
                    if (resolve) {
                        resolveClass(clazz);
                    }
                    classCache.put(name, clazz);
                    return clazz;
                } catch (ClassNotFoundException e) {
                    // 父类加载器无法加载，继续尝试自己加载
                }
            }
            
            // 尝试自己加载类
            try {
                clazz = findClass(name);
                if (resolve) {
                    resolveClass(clazz);
                }
                classCache.put(name, clazz);
                return clazz;
            } catch (ClassNotFoundException e) {
                // 自己无法加载，委派给父类加载器
                if (!shouldDelegateToParent(name)) {
                    try {
                        clazz = getParent().loadClass(name);
                        if (resolve) {
                            resolveClass(clazz);
                        }
                        classCache.put(name, clazz);
                        return clazz;
                    } catch (ClassNotFoundException ex) {
                        // 父类加载器也无法加载
                    }
                }
                throw e;
            }
        }
    }
    
    @Override
    protected Class<?> findClass(String name) throws ClassNotFoundException {
        try {
            // 尝试从URL路径中加载类
            String path = name.replace('.', '/').concat(".class");
            URL url = findResource(path);
            if (url != null) {
                try (InputStream is = url.openStream()) {
                    byte[] classBytes = readAllBytes(is);
                    return defineClass(name, classBytes, 0, classBytes.length);
                }
            }
        } catch (IOException e) {
            logger.error("Error loading class: " + name, e);
        }
        
        throw new ClassNotFoundException(name);
    }
    
    /**
     * 判断是否应该委派给父类加载器
     * @param className 类名
     * @return true表示应该委派给父类加载器
     */
    private boolean shouldDelegateToParent(String className) {
        // JDK核心类必须委派给父类加载器
        for (String prefix : PARENT_DELEGATE_PREFIXES) {
            if (className.startsWith(prefix)) {
                return true;
            }
        }
        
        // RASP核心类不委派给父类加载器
        for (String prefix : RASP_CORE_PREFIXES) {
            if (className.startsWith(prefix)) {
                return false;
            }
        }
        
        // 其他类默认不委派给父类加载器，实现类隔离
        return false;
    }
    
    /**
     * 读取输入流的所有字节
     * @param is 输入流
     * @return 字节数组
     * @throws IOException IO异常
     */
    private byte[] readAllBytes(InputStream is) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] buffer = new byte[4096];
        int bytesRead;
        while ((bytesRead = is.read(buffer)) != -1) {
            baos.write(buffer, 0, bytesRead);
        }
        return baos.toByteArray();
    }
    
    @Override
    public URL getResource(String name) {
        // 首先尝试从自己的URL路径中查找资源
        URL url = findResource(name);
        if (url != null) {
            return url;
        }
        
        // 然后委派给父类加载器
        return getParent().getResource(name);
    }
    
    @Override
    public Enumeration<URL> getResources(String name) throws IOException {
        // 合并自己和父类加载器的资源
        Enumeration<URL> parentResources = getParent().getResources(name);
        Enumeration<URL> localResources = findResources(name);
        
        return new CompoundEnumeration<>(localResources, parentResources);
    }
    
    /**
     * 获取命名空间
     * @return 命名空间
     */
    public String getNamespace() {
        return namespace;
    }
    
    /**
     * 清理类缓存
     */
    public void clearCache() {
        classCache.clear();
        logger.info("Class cache cleared for namespace: {}", namespace);
    }
    
    @Override
    public String toString() {
        return "RaspClassLoader{namespace='" + namespace + "'}";
    }
    
    /**
     * 复合枚举器，用于合并多个枚举器
     */
    private static class CompoundEnumeration<T> implements Enumeration<T> {
        private final Enumeration<T>[] enumerations;
        private int currentIndex = 0;
        
        @SafeVarargs
        public CompoundEnumeration(Enumeration<T>... enumerations) {
            this.enumerations = enumerations;
        }
        
        @Override
        public boolean hasMoreElements() {
            while (currentIndex < enumerations.length) {
                if (enumerations[currentIndex].hasMoreElements()) {
                    return true;
                }
                currentIndex++;
            }
            return false;
        }
        
        @Override
        public T nextElement() {
            if (hasMoreElements()) {
                return enumerations[currentIndex].nextElement();
            }
            throw new java.util.NoSuchElementException();
        }
    }
}
