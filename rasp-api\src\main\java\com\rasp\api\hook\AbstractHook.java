package com.rasp.api.hook;

import com.rasp.api.event.HookEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.regex.Pattern;

/**
 * Hook抽象基类
 * 提供Hook的通用实现
 */
public abstract class AbstractHook implements Hook {
    
    protected final Logger logger = LoggerFactory.getLogger(getClass());
    
    /**
     * RASP内部包名列表，这些包下的类不应该被Hook
     */
    private static final String[] RASP_INTERNAL_PACKAGES = {
        "com.rasp.",           // RASP所有内部包
        "ch.qos.logback.",     // logback日志框架
        "org.slf4j.",          // SLF4J日志接口
        "java.util.logging.",  // JDK日志
        "org.apache.logging.", // Apache日志框架
        "sun.",                // JDK内部包
        "com.sun.",            // JDK内部包
        "jdk.internal.",       // JDK内部包
        "java.lang.instrument.", // 字节码增强相关
        "java.lang.reflect."   // 反射相关
    };
    
    /**
     * Hook是否启用
     */
    private volatile boolean enabled = true;
    
    /**
     * 编译后的类名模式
     */
    private Pattern[] classNamePatterns;
    
    /**
     * 编译后的方法名模式
     */
    private Pattern[] methodNamePatterns;
    
    /**
     * 编译后的方法签名模式
     */
    private Pattern[] methodSignaturePatterns;
    
    public AbstractHook() {
        initializePatterns();
    }
    
    /**
     * 初始化模式匹配器
     */
    private void initializePatterns() {
        String[] classPatterns = getClassNamePatterns();
        if (classPatterns != null) {
            classNamePatterns = new Pattern[classPatterns.length];
            for (int i = 0; i < classPatterns.length; i++) {
                classNamePatterns[i] = Pattern.compile(wildcardToRegex(classPatterns[i]));
            }
        }
        
        String[] methodPatterns = getMethodNamePatterns();
        if (methodPatterns != null) {
            methodNamePatterns = new Pattern[methodPatterns.length];
            for (int i = 0; i < methodPatterns.length; i++) {
                methodNamePatterns[i] = Pattern.compile(wildcardToRegex(methodPatterns[i]));
            }
        }
        
        String[] signaturePatterns = getMethodSignaturePatterns();
        if (signaturePatterns != null) {
            methodSignaturePatterns = new Pattern[signaturePatterns.length];
            for (int i = 0; i < signaturePatterns.length; i++) {
                methodSignaturePatterns[i] = Pattern.compile(wildcardToRegex(signaturePatterns[i]));
            }
        }
    }
    
    /**
     * 检查是否为RASP内部类
     * @param className 类名
     * @return true表示是内部类，不应该被Hook
     */
    private boolean isRaspInternalClass(String className) {
        if (className == null) {
            return false;
        }
        
        // 检查是否匹配任何内部包名
        for (String internalPackage : RASP_INTERNAL_PACKAGES) {
            if (className.startsWith(internalPackage)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 将通配符模式转换为正则表达式
     * @param wildcard 通配符模式
     * @return 正则表达式
     */
    private String wildcardToRegex(String wildcard) {
        // 如果不包含通配符，直接使用精确匹配避免转义问题
        if (!wildcard.contains("*") && !wildcard.contains("?")) {
            return "^" + Pattern.quote(wildcard) + "$";
        }
        
        // 包含通配符时才进行转换
        StringBuilder sb = new StringBuilder(wildcard.length());
        sb.append('^');
        for (int i = 0; i < wildcard.length(); ++i) {
            char c = wildcard.charAt(i);
            switch (c) {
                case '*':
                    sb.append(".*");
                    break;
                case '?':
                    sb.append('.');
                    break;
                case '(':
                case ')':
                case '[':
                case ']':
                case '$':
                case '^':
                case '.':
                case '{':
                case '}':
                case '|':
                case '\\':
                    sb.append("\\");
                    sb.append(c);
                    break;
                default:
                    sb.append(c);
                    break;
            }
        }
        sb.append('$');
        return sb.toString();
    }
    
    @Override
    public boolean shouldHookClass(String className) {
        if (!enabled || classNamePatterns == null) {
            return false;
        }
        
        // 首先检查是否为RASP内部类，如果是则不Hook
        if (isRaspInternalClass(className)) {
            return false;
        }
        
        // 先尝试正则匹配
        for (Pattern pattern : classNamePatterns) {
            if (pattern.matcher(className).matches()) {
                return true;
            }
        }
        
        // 如果正则匹配失败，尝试简单的contains匹配作为fallback
        String[] rawPatterns = getClassNamePatterns();
        if (rawPatterns != null) {
            for (String pattern : rawPatterns) {
                // 对于不包含通配符的精确模式，使用更宽松的匹配
                if (!pattern.contains("*") && !pattern.contains("?")) {
                    // 精确匹配
                    if (className.equals(pattern)) {
                        return true;
                    }
                    // 包含匹配（处理内部类、代理类等情况）
                    if (className.contains(pattern)) {
                        return true;
                    }
                }
            }
        }
        
        return false;
    }
    
    @Override
    public boolean shouldHookMethod(String className, String methodName, String methodSignature) {
        if (!enabled) {
            return false;
        }
        
        // 检查类名
        if (!shouldHookClass(className)) {
            return false;
        }
        
        // 检查方法名
        if (methodNamePatterns != null) {
            boolean methodMatched = false;
            
            // 先尝试正则匹配
            for (Pattern pattern : methodNamePatterns) {
                if (pattern.matcher(methodName).matches()) {
                    methodMatched = true;
                    break;
                }
            }
            
            // 如果正则匹配失败，尝试简单匹配作为fallback
            if (!methodMatched) {
                String[] rawMethodPatterns = getMethodNamePatterns();
                if (rawMethodPatterns != null) {
                    for (String pattern : rawMethodPatterns) {
                        // 对于不包含通配符的精确模式，使用更宽松的匹配
                        if (!pattern.contains("*") && !pattern.contains("?")) {
                            if (methodName.equals(pattern)) {
                                methodMatched = true;
                                break;
                            }
                        }
                    }
                }
            }
            
            if (!methodMatched) {
                return false;
            }
        }
        
        // 检查方法签名
        if (methodSignaturePatterns != null && methodSignature != null) {
            boolean signatureMatched = false;
            for (Pattern pattern : methodSignaturePatterns) {
                if (pattern.matcher(methodSignature).matches()) {
                    signatureMatched = true;
                    break;
                }
            }
            if (!signatureMatched) {
                return false;
            }
        }
        
        return true;
    }
    
    @Override
    public void onMethodReturn(HookEvent event, Object returnValue) {
        if (event != null) {
            event.setReturnValue(returnValue);
            logger.debug("Method returned: {} -> {}", event, returnValue);
        }
    }
    
    @Override
    public void onMethodThrow(HookEvent event, Throwable throwable) {
        if (event != null) {
            event.setThrowable(throwable);
            logger.debug("Method threw exception: {} -> {}", event, throwable.getMessage());
        }
    }
    
    @Override
    public boolean isEnabled() {
        return enabled;
    }
    
    @Override
    public void enable() {
        this.enabled = true;
        logger.info("Hook enabled: {}", getName());
    }
    
    @Override
    public void disable() {
        this.enabled = false;
        logger.info("Hook disabled: {}", getName());
    }
    
    @Override
    public String getVersion() {
        return "1.0.0";
    }
}
