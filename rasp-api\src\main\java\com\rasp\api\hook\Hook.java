package com.rasp.api.hook;

import com.rasp.api.event.HookEvent;

/**
 * Hook接口
 * 所有Hook实现都需要实现此接口
 */
public interface Hook {
    
    /**
     * Hook名称
     * @return Hook的唯一标识名称
     */
    String getName();
    
    /**
     * Hook描述
     * @return Hook的功能描述
     */
    String getDescription();
    
    /**
     * Hook版本
     * @return Hook的版本号
     */
    String getVersion();
    
    /**
     * 获取要Hook的类名模式
     * 支持通配符匹配
     * @return 类名模式数组
     */
    String[] getClassNamePatterns();
    
    /**
     * 获取要Hook的方法名模式
     * 支持通配符匹配
     * @return 方法名模式数组
     */
    String[] getMethodNamePatterns();
    
    /**
     * 获取要Hook的方法签名模式
     * 支持通配符匹配，可选
     * @return 方法签名模式数组，null表示不限制
     */
    String[] getMethodSignaturePatterns();
    
    /**
     * 判断是否应该Hook指定的类
     * @param className 类名
     * @return true表示应该Hook，false表示不Hook
     */
    boolean shouldHookClass(String className);
    
    /**
     * 判断是否应该Hook指定的方法
     * @param className 类名
     * @param methodName 方法名
     * @param methodSignature 方法签名
     * @return true表示应该Hook，false表示不Hook
     */
    boolean shouldHookMethod(String className, String methodName, String methodSignature);
    
    /**
     * 方法执行前的Hook处理
     * @param className 类名
     * @param methodName 方法名
     * @param methodSignature 方法签名
     * @param target 目标对象实例，静态方法时为null
     * @param arguments 方法参数
     * @return Hook事件对象
     */
    HookEvent onMethodEnter(String className, String methodName, String methodSignature, 
                           Object target, Object[] arguments);
    
    /**
     * 方法正常返回时的Hook处理
     * @param event 方法进入时创建的事件对象
     * @param returnValue 方法返回值
     */
    void onMethodReturn(HookEvent event, Object returnValue);
    
    /**
     * 方法抛出异常时的Hook处理
     * @param event 方法进入时创建的事件对象
     * @param throwable 抛出的异常
     */
    void onMethodThrow(HookEvent event, Throwable throwable);
    
    /**
     * Hook是否启用
     * @return true表示启用，false表示禁用
     */
    boolean isEnabled();
    
    /**
     * 启用Hook
     */
    void enable();
    
    /**
     * 禁用Hook
     */
    void disable();
}
