package com.rasp.core.config;

/**
 * 配置功能演示程序
 * 展示如何使用RASP配置管理功能
 */
public class ConfigDemo {
    
    public static void main(String[] args) {
        System.out.println("=== RASP Configuration Demo ===\n");
        
        // 1. 基本配置信息
        demonstrateBasicConfiguration();
        
        // 2. 规则配置管理
        demonstrateRuleConfiguration();
        
        // 3. 工具类使用
        demonstrateUtilityUsage();
        
        // 4. 配置重新加载
        demonstrateConfigurationReload();

        // 5. 日志配置演示
        demonstrateLogConfiguration();

        System.out.println("\n=== Demo Completed ===");
    }
    
    /**
     * 演示基本配置功能
     */
    private static void demonstrateBasicConfiguration() {
        System.out.println("1. Basic Configuration:");
        System.out.println("----------------------");
        
        RaspConfigManager configManager = RaspConfigManager.getInstance();
        
        // 显示基本配置信息
        System.out.println("RASP Enabled: " + configManager.isRaspEnabled());
        System.out.println("Log Level: " + configManager.getProperty("rasp.log.level", "INFO"));
        System.out.println("Log File: " + configManager.getProperty("rasp.log.file", "logs/rasp-agent.log"));
        System.out.println("Max Events/Second: " + configManager.getIntProperty("rasp.performance.max_events_per_second", 1000));
        System.out.println();
    }
    
    /**
     * 演示规则配置管理
     */
    private static void demonstrateRuleConfiguration() {
        System.out.println("2. Rule Configuration:");
        System.out.println("---------------------");
        
        RaspConfigManager configManager = RaspConfigManager.getInstance();
        
        // 显示各个规则的状态
        String[] ruleNames = {
            "HttpRequestLogRule",
            "CommandExecutionRule", 
            "FileWriteRule",
            "MethodCallLogRule"
        };
        
        for (String ruleName : ruleNames) {
            boolean enabled = configManager.isRuleEnabled(ruleName);
            System.out.println(String.format("%-20s: %s", ruleName, enabled ? "ENABLED" : "DISABLED"));
        }
        System.out.println();
    }
    
    /**
     * 演示工具类使用
     */
    private static void demonstrateUtilityUsage() {
        System.out.println("3. Utility Class Usage:");
        System.out.println("-----------------------");
        
        // 获取所有规则名称
        String[] allRules = RuleConfigUtil.getAllRuleNames();
        System.out.println("All Rules: " + java.util.Arrays.toString(allRules));
        
        // 获取启用的规则
        String[] enabledRules = RuleConfigUtil.getEnabledRuleNames();
        System.out.println("Enabled Rules: " + java.util.Arrays.toString(enabledRules));
        
        // 获取禁用的规则
        String[] disabledRules = RuleConfigUtil.getDisabledRuleNames();
        System.out.println("Disabled Rules: " + java.util.Arrays.toString(disabledRules));
        
        // 检查特定规则状态
        System.out.println("HttpRequestLogRule exists: " + RuleConfigUtil.ruleExists("HttpRequestLogRule"));
        System.out.println("HttpRequestLogRule enabled: " + RuleConfigUtil.isRuleEnabled("HttpRequestLogRule"));
        System.out.println();
    }

    /**
     * 演示日志配置功能
     */
    private static void demonstrateLogConfiguration() {
        System.out.println("5. Log Configuration Demo:");
        System.out.println("-------------------------");

        // 显示当前日志配置
        System.out.println("Current log configuration:");
        RuleConfigUtil.printLogConfiguration();

        // 测试DEBUG日志
        System.out.println("\nTesting DEBUG level logging...");
        RuleConfigUtil.enableDebugLogging();

        // 创建一个测试logger来验证DEBUG级别
        org.slf4j.Logger testLogger = org.slf4j.LoggerFactory.getLogger("com.rasp.rules.command.CommandExecutionRule");
        testLogger.debug("[TEST] This is a DEBUG message - should be visible now");
        testLogger.info("[TEST] This is an INFO message");
        testLogger.warn("[TEST] This is a WARN message");

        // 切换到WARN级别
        System.out.println("\nSwitching to WARN level logging...");
        RuleConfigUtil.enableProductionLogging();

        testLogger.debug("[TEST] This DEBUG message should NOT be visible");
        testLogger.info("[TEST] This INFO message should NOT be visible");
        testLogger.warn("[TEST] This WARN message should be visible");
        testLogger.error("[TEST] This ERROR message should be visible");

        // 恢复到INFO级别
        System.out.println("\nRestoring to INFO level...");
        RuleConfigUtil.setLogLevel("INFO");

        testLogger.debug("[TEST] This DEBUG message should NOT be visible");
        testLogger.info("[TEST] This INFO message should be visible");
        testLogger.warn("[TEST] This WARN message should be visible");

        System.out.println("\nFinal log configuration:");
        RuleConfigUtil.printLogConfiguration();
        System.out.println();
    }
    
    /**
     * 演示配置重新加载
     */
    private static void demonstrateConfigurationReload() {
        System.out.println("4. Configuration Reload:");
        System.out.println("------------------------");
        
        RaspConfigManager configManager = RaspConfigManager.getInstance();
        
        // 显示重新加载前的状态
        System.out.println("Before reload:");
        System.out.println("MethodCallLogRule enabled: " + configManager.isRuleEnabled("MethodCallLogRule"));
        
        // 重新加载配置
        System.out.println("Reloading configuration...");
        configManager.reload();
        
        // 显示重新加载后的状态
        System.out.println("After reload:");
        System.out.println("MethodCallLogRule enabled: " + configManager.isRuleEnabled("MethodCallLogRule"));
        System.out.println();
    }
    
    /**
     * 演示环境模式切换（需要RuleEngine实例）
     */
    private static void demonstrateEnvironmentModes() {
        System.out.println("5. Environment Mode Demo:");
        System.out.println("-------------------------");
        
        try {
            // 注意：这需要完整的RASP环境才能运行
            System.out.println("Setting development mode...");
            RuleConfigUtil.setDevelopmentMode();
            
            System.out.println("Current rule status:");
            RuleConfigUtil.printRuleStatus();
            
            System.out.println("Setting production mode...");
            RuleConfigUtil.setProductionMode();
            
            System.out.println("Current rule status:");
            RuleConfigUtil.printRuleStatus();
            
        } catch (Exception e) {
            System.out.println("Environment mode demo requires full RASP initialization: " + e.getMessage());
        }
        System.out.println();
    }
}
