package com.rasp.api.event;

import java.util.Map;

/**
 * Hook事件基类
 * 包含Hook采集到的所有信息
 */
public class HookEvent {
    
    /**
     * 事件类型
     */
    public enum EventType {
        HTTP_REQUEST,    // HTTP请求事件
        METHOD_CALL,     // 方法调用事件
        FILE_ACCESS,     // 文件访问事件
        FILE_WRITE,      // 文件写入事件
        SQL_EXECUTION,   // SQL执行事件
        COMMAND_EXECUTION // 命令执行事件
    }
    
    /**
     * 事件发生时间戳
     */
    private long timestamp;
    
    /**
     * 事件类型
     */
    private EventType eventType;
    
    /**
     * 类名
     */
    private String className;
    
    /**
     * 方法名
     */
    private String methodName;
    
    /**
     * 方法签名
     */
    private String methodSignature;
    
    /**
     * 方法参数
     */
    private Object[] arguments;
    
    /**
     * 方法返回值
     */
    private Object returnValue;
    
    /**
     * 异常信息
     */
    private Throwable throwable;
    
    /**
     * 调用堆栈
     */
    private StackTraceElement[] stackTrace;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> attributes;
    
    /**
     * 线程信息
     */
    private String threadName;
    
    /**
     * 线程ID
     */
    private long threadId;
    
    public HookEvent() {
        this.timestamp = System.currentTimeMillis();
        this.threadName = Thread.currentThread().getName();
        this.threadId = Thread.currentThread().getId();
        this.stackTrace = Thread.currentThread().getStackTrace();
    }
    
    // Getters and Setters
    public long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
    
    public EventType getEventType() {
        return eventType;
    }
    
    public void setEventType(EventType eventType) {
        this.eventType = eventType;
    }
    
    public String getClassName() {
        return className;
    }
    
    public void setClassName(String className) {
        this.className = className;
    }
    
    public String getMethodName() {
        return methodName;
    }
    
    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }
    
    public String getMethodSignature() {
        return methodSignature;
    }
    
    public void setMethodSignature(String methodSignature) {
        this.methodSignature = methodSignature;
    }
    
    public Object[] getArguments() {
        return arguments;
    }
    
    public void setArguments(Object[] arguments) {
        this.arguments = arguments;
    }
    
    public Object getReturnValue() {
        return returnValue;
    }
    
    public void setReturnValue(Object returnValue) {
        this.returnValue = returnValue;
    }
    
    public Throwable getThrowable() {
        return throwable;
    }
    
    public void setThrowable(Throwable throwable) {
        this.throwable = throwable;
    }
    
    public StackTraceElement[] getStackTrace() {
        return stackTrace;
    }
    
    public void setStackTrace(StackTraceElement[] stackTrace) {
        this.stackTrace = stackTrace;
    }
    
    public Map<String, Object> getAttributes() {
        return attributes;
    }
    
    public void setAttributes(Map<String, Object> attributes) {
        this.attributes = attributes;
    }
    
    public String getThreadName() {
        return threadName;
    }
    
    public void setThreadName(String threadName) {
        this.threadName = threadName;
    }
    
    public long getThreadId() {
        return threadId;
    }
    
    public void setThreadId(long threadId) {
        this.threadId = threadId;
    }
    
    @Override
    public String toString() {
        return "HookEvent{" +
                "timestamp=" + timestamp +
                ", eventType=" + eventType +
                ", className='" + className + '\'' +
                ", methodName='" + methodName + '\'' +
                ", methodSignature='" + methodSignature + '\'' +
                ", threadName='" + threadName + '\'' +
                ", threadId=" + threadId +
                '}';
    }
}
