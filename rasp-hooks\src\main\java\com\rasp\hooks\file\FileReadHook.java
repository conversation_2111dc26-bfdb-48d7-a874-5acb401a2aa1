package com.rasp.hooks.file;

import com.rasp.api.event.HookEvent;
import com.rasp.api.event.FileReadEvent;
import com.rasp.api.hook.Hook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.lang.reflect.Field;

/**
 * 文件读取Hook
 * 覆盖所有文件读取操作，防护任意文件读取攻击
 */
public class FileReadHook implements Hook {
    
    private static final Logger logger = LoggerFactory.getLogger(FileReadHook.class);
    
    private boolean enabled = true;
    
    @Override
    public String getName() {
        return "FileReadHook";
    }
    
    @Override
    public String getDescription() {
        return "Hook for capturing all file read operations to prevent arbitrary file read attacks";
    }
    
    @Override
    public String getVersion() {
        return "1.0.0";
    }
    
    @Override
    public String[] getClassNamePatterns() {
        return new String[]{
            // 传统IO类
            "java.io.FileInputStream",
            "java.io.RandomAccessFile",
            "java.io.FileReader",
            "java.io.BufferedReader",
            "java.io.Scanner",
            
            // NIO类
            "java.nio.file.Files",
            "java.nio.channels.FileChannel",
            
            // Apache Commons IO
            "org.apache.commons.io.FileUtils",
            "org.apache.commons.io.IOUtils",
            
            // Spring Framework
            "org.springframework.util.FileCopyUtils",
            "org.springframework.core.io.FileSystemResource",
            "org.springframework.core.io.ClassPathResource",
            
            // 其他常见文件读取库
            "java.util.Properties",
            "java.util.zip.ZipFile",
            "java.util.jar.JarFile"
        };
    }
    
    @Override
    public String[] getMethodNamePatterns() {
        return new String[]{
            "<init>",           // 构造方法
            "read",            // 基本读取方法
            "readLine",        // 按行读取
            "readAllBytes",    // 读取所有字节
            "readAllLines",    // 读取所有行
            "readString",      // 读取字符串
            "load",            // Properties.load
            "getInputStream",  // 获取输入流
            "getReader",       // 获取Reader
            "copy",            // 文件复制
            "copyToByteArray", // 复制到字节数组
            "copyToString",    // 复制到字符串
            "next",            // Scanner.next
            "nextLine",        // Scanner.nextLine
            "hasNext",         // Scanner.hasNext
            "hasNextLine"      // Scanner.hasNextLine
        };
    }
    
    @Override
    public String[] getMethodSignaturePatterns() {
        return null; // 不限制方法签名
    }
    
    @Override
    public boolean shouldHookClass(String className) {
        if (!enabled) {
            return false;
        }
        
        String[] patterns = getClassNamePatterns();
        for (String pattern : patterns) {
            if (className.equals(pattern) || className.startsWith(pattern)) {
                return true;
            }
        }
        return false;
    }
    
    @Override
    public boolean shouldHookMethod(String className, String methodName, String methodSignature) {
        if (!enabled) {
            return false;
        }
        
        // 过滤掉一些不相关的方法
        if ("toString".equals(methodName) || "hashCode".equals(methodName) || 
            "equals".equals(methodName) || "getClass".equals(methodName)) {
            return false;
        }
        
        String[] patterns = getMethodNamePatterns();
        for (String pattern : patterns) {
            if (methodName.equals(pattern) || methodName.startsWith(pattern)) {
                return true;
            }
        }
        return false;
    }
    
    @Override
    public boolean isEnabled() {
        return enabled;
    }
    
    @Override
    public void enable() {
        this.enabled = true;
        logger.info("[FileReadHook] Hook enabled");
    }
    
    @Override
    public void disable() {
        this.enabled = false;
        logger.info("[FileReadHook] Hook disabled");
    }
    
    @Override
    public HookEvent onMethodEnter(String className, String methodName, String methodSignature,
                                   Object target, Object[] arguments) {
        FileReadEvent event = new FileReadEvent();
        event.setEventType(HookEvent.EventType.FILE_READ);
        event.setClassName(className);
        event.setMethodName(methodName);
        event.setMethodSignature(methodSignature);
        event.setArguments(arguments);
        
        try {
            String filePath = extractFilePath(className, methodName, target, arguments);
            
            if (filePath != null) {
                logger.debug("[FileReadHook] Captured file read operation: {}.{} -> {}", className, methodName, filePath);
                
                event.setFilePath(filePath);
                enrichFileEvent(event, filePath);
            }
            
        } catch (Exception e) {
            logger.error("[FileReadHook] Error in file read capture for {}.{}: {}", 
                        className, methodName, e.getMessage());
        }
        
        return event;
    }
    
    @Override
    public void onMethodReturn(HookEvent event, Object returnValue) {
        if (event instanceof FileReadEvent) {
            FileReadEvent fileEvent = (FileReadEvent) event;
            logger.debug("[FileReadHook] Method return: {}.{} -> {} (file: {})", 
                        event.getClassName(), event.getMethodName(), returnValue, fileEvent.getFilePath());
        }
    }
    
    @Override
    public void onMethodThrow(HookEvent event, Throwable throwable) {
        if (event instanceof FileReadEvent) {
            FileReadEvent fileEvent = (FileReadEvent) event;
            logger.debug("[FileReadHook] Method threw exception: {}.{} -> {} (file: {})", 
                        event.getClassName(), event.getMethodName(), throwable.getMessage(), fileEvent.getFilePath());
        }
    }
    
    /**
     * 提取文件路径
     */
    private String extractFilePath(String className, String methodName, Object target, Object[] arguments) {
        try {
            // 1. FileInputStream处理
            if ("java.io.FileInputStream".equals(className)) {
                return handleFileInputStream(methodName, target, arguments);
            }
            
            // 2. RandomAccessFile处理
            else if ("java.io.RandomAccessFile".equals(className)) {
                return handleRandomAccessFile(methodName, target, arguments);
            }
            
            // 3. NIO Files处理
            else if ("java.nio.file.Files".equals(className)) {
                return handleNIOFiles(methodName, arguments);
            }
            
            // 4. FileReader处理
            else if ("java.io.FileReader".equals(className)) {
                return handleFileReader(methodName, target, arguments);
            }
            
            // 5. Scanner处理
            else if ("java.util.Scanner".equals(className)) {
                return handleScanner(methodName, target, arguments);
            }
            
            // 6. Apache Commons FileUtils处理
            else if ("org.apache.commons.io.FileUtils".equals(className)) {
                return handleApacheFileUtils(methodName, arguments);
            }
            
            // 7. Properties处理
            else if ("java.util.Properties".equals(className)) {
                return handleProperties(methodName, target, arguments);
            }
            
            // 8. Spring Resource处理
            else if (className.contains("Resource")) {
                return handleSpringResource(methodName, target, arguments);
            }
            
            // 9. 其他常见文件读取类
            else if (className.contains("InputStream") || className.contains("Reader")) {
                return handleGenericFileReader(methodName, target, arguments);
            }
            
        } catch (Exception e) {
            logger.debug("Failed to extract file path from {}.{}: {}", className, methodName, e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 处理FileInputStream
     */
    private String handleFileInputStream(String methodName, Object target, Object[] arguments) {
        if ("<init>".equals(methodName) && arguments != null && arguments.length > 0) {
            Object fileArg = arguments[0];
            if (fileArg instanceof File) {
                return ((File) fileArg).getPath();
            } else if (fileArg instanceof String) {
                return (String) fileArg;
            }
        } else if ("read".equals(methodName) && target != null) {
            // 从已创建的FileInputStream中提取路径
            return extractPathFromFileInputStream(target);
        }
        return null;
    }
    
    /**
     * 处理RandomAccessFile
     */
    private String handleRandomAccessFile(String methodName, Object target, Object[] arguments) {
        if ("<init>".equals(methodName) && arguments != null && arguments.length > 0) {
            Object fileArg = arguments[0];
            if (fileArg instanceof File) {
                return ((File) fileArg).getPath();
            } else if (fileArg instanceof String) {
                return (String) fileArg;
            }
        } else if ("read".equals(methodName) && target != null) {
            return extractPathFromRandomAccessFile(target);
        }
        return null;
    }
    
    /**
     * 处理NIO Files
     */
    private String handleNIOFiles(String methodName, Object[] arguments) {
        if (("readAllBytes".equals(methodName) || "readAllLines".equals(methodName) || 
             "readString".equals(methodName)) && arguments != null && arguments.length > 0) {
            Object pathArg = arguments[0];
            if (pathArg != null) {
                return pathArg.toString();
            }
        }
        return null;
    }
    
    /**
     * 丰富文件事件信息
     */
    private void enrichFileEvent(FileReadEvent event, String filePath) {
        try {
            File file = new File(filePath);
            String absolutePath = file.getAbsolutePath();
            event.setAbsolutePath(absolutePath);
            
            // 判断是否为绝对路径
            boolean isAbsolute = file.isAbsolute();
            event.setIsAbsolutePath(isAbsolute);
            
            // 设置文件是否存在
            boolean exists = file.exists();
            event.setFileExists(exists);
            
            if (exists) {
                // 设置文件大小
                event.setFileSize(file.length());
                
                // 设置规范路径
                try {
                    event.setCanonicalPath(file.getCanonicalPath());
                } catch (Exception e) {
                    logger.debug("Failed to get canonical path: {}", e.getMessage());
                }
            }
            
            // 提取文件扩展名
            String fileName = file.getName();
            int lastDot = fileName.lastIndexOf('.');
            if (lastDot > 0 && lastDot < fileName.length() - 1) {
                String extension = fileName.substring(lastDot + 1);
                event.setFileExtension(extension);
            }
            
            // 设置父目录
            String parent = file.getParent();
            if (parent != null) {
                event.setParentDirectory(parent);
            }
            
        } catch (Exception e) {
            logger.debug("Error enriching file read event: {}", e.getMessage());
        }
    }
    
    /**
     * 处理FileReader
     */
    private String handleFileReader(String methodName, Object target, Object[] arguments) {
        if ("<init>".equals(methodName) && arguments != null && arguments.length > 0) {
            Object fileArg = arguments[0];
            if (fileArg instanceof File) {
                return ((File) fileArg).getPath();
            } else if (fileArg instanceof String) {
                return (String) fileArg;
            }
        }
        return null;
    }

    /**
     * 处理Scanner
     */
    private String handleScanner(String methodName, Object target, Object[] arguments) {
        if ("<init>".equals(methodName) && arguments != null && arguments.length > 0) {
            Object fileArg = arguments[0];
            if (fileArg instanceof File) {
                return ((File) fileArg).getPath();
            }
        }
        return null;
    }

    /**
     * 处理Apache Commons FileUtils
     */
    private String handleApacheFileUtils(String methodName, Object[] arguments) {
        if (("readFileToString".equals(methodName) || "readFileToByteArray".equals(methodName) ||
             "readLines".equals(methodName)) && arguments != null && arguments.length > 0) {
            Object fileArg = arguments[0];
            if (fileArg instanceof File) {
                return ((File) fileArg).getPath();
            } else if (fileArg instanceof String) {
                return (String) fileArg;
            }
        }
        return null;
    }

    /**
     * 处理Properties
     */
    private String handleProperties(String methodName, Object target, Object[] arguments) {
        if ("load".equals(methodName) && arguments != null && arguments.length > 0) {
            // Properties.load通常接收InputStream，需要从InputStream中提取路径
            Object inputStreamArg = arguments[0];
            if (inputStreamArg != null) {
                return extractPathFromInputStream(inputStreamArg);
            }
        }
        return null;
    }

    /**
     * 处理Spring Resource
     */
    private String handleSpringResource(String methodName, Object target, Object[] arguments) {
        if ("<init>".equals(methodName) && arguments != null && arguments.length > 0) {
            Object pathArg = arguments[0];
            if (pathArg instanceof String) {
                return (String) pathArg;
            }
        } else if ("getInputStream".equals(methodName) && target != null) {
            return extractPathFromSpringResource(target);
        }
        return null;
    }

    /**
     * 处理通用文件读取类
     */
    private String handleGenericFileReader(String methodName, Object target, Object[] arguments) {
        if ("<init>".equals(methodName) && arguments != null && arguments.length > 0) {
            Object fileArg = arguments[0];
            if (fileArg instanceof File) {
                return ((File) fileArg).getPath();
            } else if (fileArg instanceof String) {
                return (String) fileArg;
            }
        }
        return null;
    }

    /**
     * 从FileInputStream中提取路径
     */
    private String extractPathFromFileInputStream(Object fileInputStream) {
        try {
            Field pathField = fileInputStream.getClass().getDeclaredField("path");
            pathField.setAccessible(true);
            Object path = pathField.get(fileInputStream);
            return path != null ? path.toString() : null;
        } catch (Exception e) {
            logger.debug("Failed to extract path from FileInputStream: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从RandomAccessFile中提取路径
     */
    private String extractPathFromRandomAccessFile(Object randomAccessFile) {
        try {
            Field pathField = randomAccessFile.getClass().getDeclaredField("path");
            pathField.setAccessible(true);
            Object path = pathField.get(randomAccessFile);
            return path != null ? path.toString() : null;
        } catch (Exception e) {
            logger.debug("Failed to extract path from RandomAccessFile: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从InputStream中提取路径
     */
    private String extractPathFromInputStream(Object inputStream) {
        try {
            // 如果是FileInputStream
            if (inputStream.getClass().getName().equals("java.io.FileInputStream")) {
                return extractPathFromFileInputStream(inputStream);
            }
            // 其他类型的InputStream暂时无法提取路径
            return null;
        } catch (Exception e) {
            logger.debug("Failed to extract path from InputStream: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从Spring Resource中提取路径
     */
    private String extractPathFromSpringResource(Object resource) {
        try {
            // 尝试调用getFile()方法
            java.lang.reflect.Method getFileMethod = resource.getClass().getMethod("getFile");
            Object file = getFileMethod.invoke(resource);
            if (file instanceof File) {
                return ((File) file).getPath();
            }
        } catch (Exception e) {
            // 如果getFile()失败，尝试getFilename()
            try {
                java.lang.reflect.Method getFilenameMethod = resource.getClass().getMethod("getFilename");
                Object filename = getFilenameMethod.invoke(resource);
                return filename != null ? filename.toString() : null;
            } catch (Exception ex) {
                logger.debug("Failed to extract path from Spring Resource: {}", ex.getMessage());
            }
        }
        return null;
    }
}
