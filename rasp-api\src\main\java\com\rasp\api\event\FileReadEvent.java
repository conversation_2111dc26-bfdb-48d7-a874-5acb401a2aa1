package com.rasp.api.event;

/**
 * 文件读取事件
 * 用于捕获和分析文件读取操作，防护任意文件读取攻击
 */
public class FileReadEvent extends HookEvent {
    
    private String filePath;
    private String absolutePath;
    private boolean isAbsolutePath;
    private String fileExtension;
    private long fileSize;
    private boolean fileExists;
    private String canonicalPath;
    private String parentDirectory;
    
    public String getFilePath() {
        return filePath;
    }
    
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
    
    public String getAbsolutePath() {
        return absolutePath;
    }
    
    public void setAbsolutePath(String absolutePath) {
        this.absolutePath = absolutePath;
    }
    
    public boolean isAbsolutePath() {
        return isAbsolutePath;
    }
    
    public void setIsAbsolutePath(boolean absolutePath) {
        isAbsolutePath = absolutePath;
    }
    
    public String getFileExtension() {
        return fileExtension;
    }
    
    public void setFileExtension(String fileExtension) {
        this.fileExtension = fileExtension;
    }
    
    public long getFileSize() {
        return fileSize;
    }
    
    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }
    
    public boolean isFileExists() {
        return fileExists;
    }
    
    public void setFileExists(boolean fileExists) {
        this.fileExists = fileExists;
    }
    
    public String getCanonicalPath() {
        return canonicalPath;
    }
    
    public void setCanonicalPath(String canonicalPath) {
        this.canonicalPath = canonicalPath;
    }
    
    public String getParentDirectory() {
        return parentDirectory;
    }
    
    public void setParentDirectory(String parentDirectory) {
        this.parentDirectory = parentDirectory;
    }
    
    @Override
    public String toString() {
        return String.format("FileReadEvent{filePath='%s', absolutePath='%s', isAbsolute=%s, extension='%s', exists=%s, size=%d, %s}",
                           filePath, absolutePath, isAbsolutePath, fileExtension, fileExists, fileSize, super.toString());
    }
}
