package com.rasp.core.enhance;

import org.objectweb.asm.*;
import org.objectweb.asm.commons.AdviceAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 类增强器
 * 使用ASM进行字节码增强，在目标方法中插入Hook代码
 */
public class ClassEnhancer {
    
    private static final Logger logger = LoggerFactory.getLogger(ClassEnhancer.class);
    
    /**
     * SpyCallbackImpl类的内部名称
     */
    private static final String SPY_CALLBACK_IMPL_INTERNAL_NAME = "com/rasp/core/spy/SpyCallbackImpl";
    
    /**
     * 增强类字节码
     * @param className 类名
     * @param classBytes 原始字节码
     * @return 增强后的字节码
     */
    public static byte[] enhance(String className, byte[] classBytes) {
        try {
            logger.debug("Starting enhancement for class: {}", className);

            ClassReader classReader = new ClassReader(classBytes);
            // 使用自定义的ClassWriter来处理类型解析问题
            // 不使用COMPUTE_FRAMES，避免栈帧映射表问题
            ClassWriter classWriter = new SafeClassWriter(classReader, ClassWriter.COMPUTE_MAXS);

            ClassVisitor classVisitor = new RaspClassVisitor(Opcodes.ASM9, classWriter, className);
            // 使用EXPAND_FRAMES，让LocalVariablesSorter正常工作
            classReader.accept(classVisitor, ClassReader.EXPAND_FRAMES);

            byte[] enhancedBytes = classWriter.toByteArray();
            logger.debug("Successfully enhanced class: {}", className);
            return enhancedBytes;
        } catch (Exception e) {
            // 特别处理TypeNotPresentException
            if (e.getClass().getSimpleName().equals("TypeNotPresentException")) {
                logger.warn("Type resolution failed for class: {}, error: {}, using original bytecode", className, e.getMessage());
                return classBytes;
            }
            logger.warn("Failed to enhance class: {}, error: {}, using original bytecode", className, e.getMessage());
            return classBytes; // 返回原始字节码，确保不影响应用运行
        }
    }
    
    /**
     * RASP类访问器
     */
    private static class RaspClassVisitor extends ClassVisitor {
        
        private final String className;
        
        public RaspClassVisitor(int api, ClassVisitor classVisitor, String className) {
            super(api, classVisitor);
            this.className = className.replace('/', '.');
        }
        
        @Override
        public MethodVisitor visitMethod(int access, String name, String descriptor, 
                                       String signature, String[] exceptions) {
            MethodVisitor methodVisitor = super.visitMethod(access, name, descriptor, signature, exceptions);
            
            // 跳过静态初始化块等特殊方法
            if (shouldSkipMethod(name)) {
                return methodVisitor;
            }
            
            return new RaspMethodVisitor(Opcodes.ASM9, methodVisitor, access, name, descriptor, className);
        }
        
        /**
         * 判断是否应该跳过方法增强
         * @param methodName 方法名
         * @return true表示跳过
         */
        private boolean shouldSkipMethod(String methodName) {
            return "<clinit>".equals(methodName);
        }
    }
    
    /**
     * RASP方法访问器
     */
    private static class RaspMethodVisitor extends AdviceAdapter {
        
        private final String className;
        private final String methodName;
        private final String methodDescriptor;
        private final boolean isStatic;
        
        private int eventsLocalIndex = -1;
        
        public RaspMethodVisitor(int api, MethodVisitor methodVisitor, int access, 
                               String methodName, String methodDescriptor, String className) {
            super(api, methodVisitor, access, methodName, methodDescriptor);
            this.className = className;
            this.methodName = methodName;
            this.methodDescriptor = methodDescriptor;
            this.isStatic = (access & Opcodes.ACC_STATIC) != 0;
        }
        
        @Override
        protected void onMethodEnter() {
            try {
                // 调用Spy.onMethodEnter()方法
                // 准备参数：className, methodName, methodDescriptor, target, arguments

                // 1. className (String)
                mv.visitLdcInsn(className);

                // 2. methodName (String)
                mv.visitLdcInsn(methodName);

                // 3. methodDescriptor (String)
                mv.visitLdcInsn(methodDescriptor);

                // 4. target (Object) - this对象或null（静态方法）
                if (isStatic) {
                    mv.visitInsn(ACONST_NULL);
                } else {
                    mv.visitVarInsn(ALOAD, 0); // this
                }

                // 5. arguments (Object[])
                loadArguments();

                // 调用SpyCallbackImpl.onMethodEnter()
                mv.visitMethodInsn(INVOKESTATIC, SPY_CALLBACK_IMPL_INTERNAL_NAME, "onMethodEnter",
                    "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;[Ljava/lang/Object;)[Ljava/lang/Object;", false);

                // 保存返回的事件数组到局部变量
                eventsLocalIndex = newLocal(Type.getType("[Ljava/lang/Object;"));
                mv.visitVarInsn(ASTORE, eventsLocalIndex);

            } catch (Exception e) {
                logger.debug("Error in onMethodEnter for {}.{}: {}", className, methodName, e.getMessage());
            }
        }


        
        @Override
        protected void onMethodExit(int opcode) {
            try {
                // 如果有事件需要处理，调用SpyCallbackImpl.onMethodExit()
                if (eventsLocalIndex != -1) {
                    // 准备参数：className, methodName, methodDescriptor, returnValue

                    // 1. className (String)
                    mv.visitLdcInsn(className);

                    // 2. methodName (String)
                    mv.visitLdcInsn(methodName);

                    // 3. methodDescriptor (String)
                    mv.visitLdcInsn(methodDescriptor);

                    // 4. returnValue (Object) - 暂时传null
                    mv.visitInsn(ACONST_NULL);

                    // 调用SpyCallbackImpl.onMethodExit()
                    mv.visitMethodInsn(INVOKESTATIC, SPY_CALLBACK_IMPL_INTERNAL_NAME, "onMethodExit",
                        "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;)V", false);
                }

            } catch (Exception e) {
                logger.debug("Error in onMethodExit for {}.{}: {}", className, methodName, e.getMessage());
            }
        }
        
        /**
         * 加载方法参数到数组中
         */
        private void loadArguments() {
            Type[] argumentTypes = Type.getArgumentTypes(methodDescriptor);
            
            // 创建参数数组
            push(argumentTypes.length);
            mv.visitTypeInsn(ANEWARRAY, "java/lang/Object");
            
            int localIndex = isStatic ? 0 : 1; // 非静态方法跳过this
            
            for (int i = 0; i < argumentTypes.length; i++) {
                mv.visitInsn(DUP); // 复制数组引用
                push(i); // 数组索引
                
                Type argumentType = argumentTypes[i];
                mv.visitVarInsn(argumentType.getOpcode(ILOAD), localIndex);
                
                // 基本类型需要装箱
                if (argumentType.getSort() != Type.OBJECT && argumentType.getSort() != Type.ARRAY) {
                    box(argumentType);
                }
                
                mv.visitInsn(AASTORE); // 存储到数组
                localIndex += argumentType.getSize();
            }
        }
    }

    /**
     * 安全的ClassWriter，处理类型解析错误
     */
    private static class SafeClassWriter extends ClassWriter {

        private final ClassLoader classLoader;

        public SafeClassWriter(ClassReader classReader, int flags) {
            super(classReader, flags);
            this.classLoader = Thread.currentThread().getContextClassLoader();
        }

        @Override
        protected String getCommonSuperClass(String type1, String type2) {
            try {
                return super.getCommonSuperClass(type1, type2);
            } catch (RuntimeException e) {
                // 当无法解析类型时，使用安全的默认策略
                logger.debug("Cannot resolve common super class for {} and {}, using Object as fallback: {}",
                           type1, type2, e.getMessage());

                // 尝试使用类加载器解析
                try {
                    Class<?> class1 = loadClass(type1);
                    Class<?> class2 = loadClass(type2);
                    return getCommonSuperClassSafe(class1, class2);
                } catch (Exception ex) {
                    // 如果仍然失败，返回Object作为安全的公共超类
                    logger.debug("Fallback to Object for {} and {}: {}", type1, type2, ex.getMessage());
                    return "java/lang/Object";
                }
            } catch (Exception e2) {
                // 特别处理TypeNotPresentException
                if (e2.getClass().getSimpleName().equals("TypeNotPresentException")) {
                    logger.debug("Type not present when resolving common super class for {} and {}, using Object as fallback: {}",
                               type1, type2, e2.getMessage());
                    return "java/lang/Object";
                }
                // 其他异常也返回Object作为安全的公共超类
                logger.debug("Other exception when resolving common super class for {} and {}, using Object as fallback: {}",
                           type1, type2, e2.getMessage());
                return "java/lang/Object";
            }
        }

        /**
         * 安全地获取两个类的公共超类
         */
        private String getCommonSuperClassSafe(Class<?> class1, Class<?> class2) {
            if (class1.isAssignableFrom(class2)) {
                return class1.getName().replace('.', '/');
            }
            if (class2.isAssignableFrom(class1)) {
                return class2.getName().replace('.', '/');
            }

            // 查找公共超类
            Class<?> superClass1 = class1.getSuperclass();
            while (superClass1 != null) {
                if (superClass1.isAssignableFrom(class2)) {
                    return superClass1.getName().replace('.', '/');
                }
                superClass1 = superClass1.getSuperclass();
            }

            // 如果没有找到公共超类，返回Object
            return "java/lang/Object";
        }

        /**
         * 安全地加载类
         */
        private Class<?> loadClass(String internalName) throws ClassNotFoundException {
            String className = internalName.replace('/', '.');

            // 尝试多种类加载器
            ClassLoader[] loaders = {
                classLoader,
                Thread.currentThread().getContextClassLoader(),
                ClassLoader.getSystemClassLoader(),
                SafeClassWriter.class.getClassLoader()
            };

            for (ClassLoader loader : loaders) {
                if (loader != null) {
                    try {
                        return Class.forName(className, false, loader);
                    } catch (ClassNotFoundException | NoClassDefFoundError e) {
                        // 继续尝试下一个类加载器
                    }
                }
            }

            throw new ClassNotFoundException("Cannot load class: " + className);
        }
    }
}
