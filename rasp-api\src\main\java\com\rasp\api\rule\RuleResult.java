package com.rasp.api.rule;

/**
 * 规则处理结果
 */
public class RuleResult {
    
    /**
     * 规则处理动作
     */
    public enum Action {
        ALLOW,      // 允许继续执行
        BLOCK,      // 阻止执行
        LOG,        // 记录日志
        ALERT       // 告警
    }
    
    /**
     * 风险等级
     */
    public enum RiskLevel {
        LOW,        // 低风险
        MEDIUM,     // 中风险
        HIGH,       // 高风险
        CRITICAL    // 严重风险
    }
    
    /**
     * 处理动作
     */
    private Action action;
    
    /**
     * 风险等级
     */
    private RiskLevel riskLevel;
    
    /**
     * 规则名称
     */
    private String ruleName;
    
    /**
     * 处理消息
     */
    private String message;
    
    /**
     * 详细信息
     */
    private String details;
    
    /**
     * 是否匹配规则
     */
    private boolean matched;
    
    /**
     * 处理时间戳
     */
    private long timestamp;
    
    public RuleResult() {
        this.timestamp = System.currentTimeMillis();
        this.matched = false;
        this.action = Action.ALLOW;
        this.riskLevel = RiskLevel.LOW;
    }
    
    public RuleResult(String ruleName, boolean matched, Action action, RiskLevel riskLevel, String message) {
        this();
        this.ruleName = ruleName;
        this.matched = matched;
        this.action = action;
        this.riskLevel = riskLevel;
        this.message = message;
    }
    
    /**
     * 创建允许结果
     */
    public static RuleResult allow(String ruleName) {
        return new RuleResult(ruleName, false, Action.ALLOW, RiskLevel.LOW, "Allowed");
    }
    
    /**
     * 创建阻止结果
     */
    public static RuleResult block(String ruleName, RiskLevel riskLevel, String message) {
        return new RuleResult(ruleName, true, Action.BLOCK, riskLevel, message);
    }
    
    /**
     * 创建日志结果
     */
    public static RuleResult log(String ruleName, RiskLevel riskLevel, String message) {
        return new RuleResult(ruleName, true, Action.LOG, riskLevel, message);
    }
    
    /**
     * 创建告警结果
     */
    public static RuleResult alert(String ruleName, RiskLevel riskLevel, String message) {
        return new RuleResult(ruleName, true, Action.ALERT, riskLevel, message);
    }
    
    // Getters and Setters
    public Action getAction() {
        return action;
    }
    
    public void setAction(Action action) {
        this.action = action;
    }
    
    public RiskLevel getRiskLevel() {
        return riskLevel;
    }
    
    public void setRiskLevel(RiskLevel riskLevel) {
        this.riskLevel = riskLevel;
    }
    
    public String getRuleName() {
        return ruleName;
    }
    
    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public String getDetails() {
        return details;
    }
    
    public void setDetails(String details) {
        this.details = details;
    }
    
    public boolean isMatched() {
        return matched;
    }
    
    public void setMatched(boolean matched) {
        this.matched = matched;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
    
    @Override
    public String toString() {
        return "RuleResult{" +
                "action=" + action +
                ", riskLevel=" + riskLevel +
                ", ruleName='" + ruleName + '\'' +
                ", message='" + message + '\'' +
                ", matched=" + matched +
                ", timestamp=" + timestamp +
                '}';
    }
}
