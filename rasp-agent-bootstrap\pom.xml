<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.rasp</groupId>
        <artifactId>rasp-agent</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>rasp-agent-bootstrap</artifactId>
    <name>RASP Agent Bootstrap</name>
    <description>RASP Agent Bootstrap</description>

    <dependencies>
        <dependency>
            <groupId>com.rasp</groupId>
            <artifactId>rasp-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.rasp</groupId>
            <artifactId>rasp-hooks</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.rasp</groupId>
            <artifactId>rasp-rules</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <transformers>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                    <manifestEntries>
                                        <Premain-Class>com.rasp.agent.RaspAgent</Premain-Class>
                                        <Agent-Class>com.rasp.agent.RaspAgent</Agent-Class>
                                        <Can-Redefine-Classes>true</Can-Redefine-Classes>
                                        <Can-Retransform-Classes>true</Can-Retransform-Classes>
                                    </manifestEntries>
                                </transformer>
                            </transformers>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
