# RASP规则配置功能

## 🎯 功能概述

RASP Agent现在支持通过配置文件来管理规则的启用和禁用状态，提供了更灵活和可维护的规则管理方式。

## 🚀 快速开始

### 1. 创建配置文件

在项目根目录或类路径下创建 `rasp-config.properties` 文件：

```properties
# 规则配置
rasp.rule.HttpRequestLogRule.enabled=true
rasp.rule.CommandExecutionRule.enabled=true
rasp.rule.FileWriteRule.enabled=true
rasp.rule.MethodCallLogRule.enabled=false

# 全局配置
rasp.enabled=true
rasp.log.level=INFO
```

### 2. 启动应用

```bash
java -javaagent:rasp-agent-bootstrap-1.0.0.jar -jar your-app.jar
```

### 3. 指定配置文件

```bash
java -javaagent:rasp-agent-bootstrap-1.0.0.jar \
     -Drasp.config.file=/path/to/custom-config.properties \
     -jar your-app.jar
```

## 📋 可用规则列表

| 规则名称 | 功能描述 | 默认状态 | 性能影响 |
|---------|----------|----------|----------|
| `HttpRequestLogRule` | HTTP请求日志记录 | ✅ 启用 | 低 |
| `CommandExecutionRule` | 命令执行安全检测 | ✅ 启用 | 中 |
| `FileWriteRule` | 文件写入操作监控 | ✅ 启用 | 中 |
| `MethodCallLogRule` | 方法调用详细日志 | ❌ 禁用 | 高 |

## 🔧 配置方式

### 方式1: 配置文件

```properties
# 启用HTTP请求监控
rasp.rule.HttpRequestLogRule.enabled=true

# 禁用详细的方法调用日志（性能考虑）
rasp.rule.MethodCallLogRule.enabled=false
```

### 方式2: 系统属性

```bash
-Drasp.rule.MethodCallLogRule.enabled=true
```

### 方式3: 运行时API

```java
import com.rasp.core.config.RuleConfigUtil;

// 启用规则
RuleConfigUtil.enableRule("MethodCallLogRule");

// 禁用规则
RuleConfigUtil.disableRule("HttpRequestLogRule");

// 批量操作
RuleConfigUtil.enableRules("HttpRequestLogRule", "CommandExecutionRule");
```

## 🌍 环境配置

### 开发环境

使用 `rasp-config-dev.properties`：

```properties
rasp.log.level=DEBUG
rasp.rule.HttpRequestLogRule.enabled=true
rasp.rule.CommandExecutionRule.enabled=true
rasp.rule.FileWriteRule.enabled=true
rasp.rule.MethodCallLogRule.enabled=true  # 开发环境启用详细日志
rasp.debug.enabled=true
```

启动命令：
```bash
java -javaagent:rasp-agent.jar \
     -Drasp.config.file=config/rasp-config-dev.properties \
     -jar your-app.jar
```

### 生产环境

使用 `rasp-config-prod.properties`：

```properties
rasp.log.level=WARN
rasp.rule.HttpRequestLogRule.enabled=true
rasp.rule.CommandExecutionRule.enabled=true
rasp.rule.FileWriteRule.enabled=true
rasp.rule.MethodCallLogRule.enabled=false  # 生产环境禁用详细日志
rasp.debug.enabled=false
rasp.performance.max_events_per_second=500
```

启动命令：
```bash
java -javaagent:rasp-agent.jar \
     -Drasp.config.file=config/rasp-config-prod.properties \
     -jar your-app.jar
```

## 🛠️ 运行时管理

### 查看规则状态

```java
// 打印所有规则状态
RuleConfigUtil.printRuleStatus();

// 检查特定规则
boolean enabled = RuleConfigUtil.isRuleEnabled("HttpRequestLogRule");
```

### 环境模式切换

```java
// 切换到开发模式（启用所有规则）
RuleConfigUtil.setDevelopmentMode();

// 切换到生产模式（禁用详细日志）
RuleConfigUtil.setProductionMode();
```

### 重新加载配置

```java
// 重新加载配置文件
RuleConfigUtil.reloadConfiguration();
```

## 📊 配置验证

### 启动日志检查

```
INFO  c.r.c.config.RaspConfigManager - Configuration loaded from: rasp-config.properties
INFO  c.r.c.config.RaspConfigManager - Rule HttpRequestLogRule: ENABLED
INFO  c.r.c.config.RaspConfigManager - Rule CommandExecutionRule: ENABLED
INFO  c.r.c.config.RaspConfigManager - Rule MethodCallLogRule: DISABLED
INFO  c.r.c.RaspCore - Rules configuration completed from config file
```

### 运行时检查

```java
// 运行配置演示程序
java -cp rasp-core.jar com.rasp.core.config.ConfigDemo
```

## 🎯 最佳实践

### 1. 性能优化
- 生产环境禁用 `MethodCallLogRule`
- 根据负载调整 `max_events_per_second`
- 设置合适的日志级别

### 2. 安全考虑
- 生产环境必须启用 `CommandExecutionRule`
- 根据需要启用 `FileWriteRule`
- 定期检查规则配置

### 3. 运维管理
- 为不同环境准备专门的配置文件
- 使用版本控制管理配置文件
- 建立配置变更审批流程

## 🔍 故障排除

### 配置不生效
1. 检查配置文件路径和格式
2. 查看启动日志中的配置加载信息
3. 验证规则名称拼写

### 性能问题
1. 检查是否启用了 `MethodCallLogRule`
2. 调整事件处理频率限制
3. 优化日志级别设置

### 规则未工作
1. 确认规则已启用：`RuleConfigUtil.isRuleEnabled("RuleName")`
2. 检查相关Hook是否正常工作
3. 查看规则处理日志

## 📚 更多信息

- 详细配置指南：[docs/Rule-Configuration-Guide.md](docs/Rule-Configuration-Guide.md)
- 架构文档：[docs/RASP-Agent-Complete-Guide.md](docs/RASP-Agent-Complete-Guide.md)
- Hook开发指南：[docs/Hook-Development-Guide.md](docs/Hook-Development-Guide.md)
