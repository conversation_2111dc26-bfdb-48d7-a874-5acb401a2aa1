# RASP Agent 完整技术文档

## 📋 目录

1. [项目概述](#1-项目概述)
2. [项目架构](#2-项目架构)
3. [模块详解](#3-模块详解)
4. [核心原理](#4-核心原理)
5. [开发指南](#5-开发指南)
6. [部署运行](#6-部署运行)
7. [故障排查](#7-故障排查)
8. [扩展开发](#8-扩展开发)

---

## 1. 项目概述

### 1.1 什么是RASP

**RASP (Runtime Application Self-Protection)** 是一种运行时应用自我保护技术，它通过在应用程序运行时插入安全检测代码，实时监控和阻断恶意攻击。

### 1.2 项目特点

- ✅ **零代码侵入**：通过Java Agent技术，无需修改应用代码
- ✅ **实时防护**：运行时检测，即时响应安全威胁
- ✅ **类加载器隔离**：避免与应用程序依赖冲突
- ✅ **可扩展架构**：支持自定义Hook和Rule
- ✅ **自动注册机制**：新增组件无需手动配置

### 1.3 支持的攻击类型

- 🛡️ **命令注入**：检测和阻断恶意命令执行
- 🛡️ **SQL注入**：监控数据库查询安全
- 🛡️ **文件操作**：防护敏感文件访问
- 🛡️ **网络请求**：监控外部网络连接

---

## 2. 项目架构

### 2.1 整体架构图

```mermaid
graph TB
    A[Java应用程序] --> B[Java Agent]
    B --> C[Bootstrap ClassLoader]
    C --> D[Spy类]
    D --> E[隔离ClassLoader]
    E --> F[RASP Core]
    F --> G[Hook Manager]
    F --> H[Rule Engine]
    G --> I[HTTP Hook]
    G --> J[Command Hook]
    H --> K[Security Rules]
    H --> L[Log Rules]
```

### 2.2 技术栈

| 组件 | 技术 | 版本 |
|------|------|------|
| 核心框架 | Java | 8+ |
| 构建工具 | Maven | 3.6+ |
| 字节码操作 | ASM | 9.x |
| 日志框架 | SLF4J + Logback | 1.7.x |
| 测试框架 | JUnit | 4.x |

---

## 3. 模块详解

### 3.1 项目结构

```
rasp-agent/
├── rasp-api/                    # 核心API定义
├── rasp-core/                   # 核心引擎
├── rasp-agent-bootstrap/        # Agent启动器
├── rasp-hooks/                  # Hook实现
├── rasp-rules/                  # Rule实现
├── vuln-test/                   # 测试应用
└── pom.xml                      # 主POM文件
```

### 3.2 rasp-api 模块

**作用**：定义核心接口和数据结构

```
rasp-api/src/main/java/com/rasp/api/
├── event/                       # 事件定义
│   ├── HookEvent.java          # 基础事件类
│   ├── HttpRequestEvent.java   # HTTP请求事件
│   └── CommandExecutionEvent.java # 命令执行事件
├── hook/                        # Hook接口
│   └── Hook.java               # Hook基础接口
├── rule/                        # Rule接口
│   ├── Rule.java               # Rule基础接口
│   └── RuleResult.java         # 规则执行结果
└── context/                     # 上下文管理
    └── RequestContext.java     # 请求上下文
```

**核心接口示例**：

```java
// Hook接口
public interface Hook {
    String getName();
    String getDescription();
    HookEvent.EventType[] getSupportedEventTypes();
    List<HookEvent> onMethodEnter(String className, String methodName, 
                                  String signature, Object[] args);
}

// Rule接口
public interface Rule {
    String getName();
    String getDescription();
    HookEvent.EventType[] getSupportedEventTypes();
    RuleResult evaluate(HookEvent event);
}
```

### 3.3 rasp-core 模块

**作用**：核心引擎，负责Hook管理、Rule执行、类加载器隔离

```
rasp-core/src/main/java/com/rasp/core/
├── RaspCore.java               # 核心启动类
├── classloader/                # 类加载器
│   └── RaspClassLoader.java   # 隔离类加载器
├── enhance/                    # 字节码增强
│   └── ClassEnhancer.java     # 类增强器
├── hook/                       # Hook管理
│   └── HookManager.java       # Hook管理器
├── rule/                       # Rule管理
│   └── RuleEngine.java        # 规则引擎
└── spy/                        # 回调机制
    ├── Spy.java               # 回调入口
    └── SpyCallbackImpl.java   # 回调实现
```

### 3.4 rasp-agent-bootstrap 模块

**作用**：Agent启动器，负责初始化和类加载器设置

```
rasp-agent-bootstrap/src/main/java/com/rasp/
├── agent/
│   └── RaspAgent.java          # Agent入口
└── core/spy/                   # Spy类（Bootstrap级别）
    ├── Spy.java               # 回调桥接
    └── SpyCallbackImpl.java   # 回调实现
```

### 3.5 rasp-hooks 模块

**作用**：Hook实现，负责具体的方法拦截

```
rasp-hooks/src/main/java/com/rasp/hooks/
├── http/
│   └── HttpRequestHook.java    # HTTP请求Hook
└── command/
    └── CommandExecutionHook.java # 命令执行Hook
```

### 3.6 rasp-rules 模块

**作用**：Rule实现，负责安全检测逻辑

```
rasp-rules/src/main/java/com/rasp/rules/
└── demo/
    ├── HttpRequestLogRule.java     # HTTP日志规则
    ├── CommandExecutionRule.java   # 命令执行安全规则
    └── MethodCallLogRule.java      # 方法调用日志规则
```

---

## 4. 核心原理

### 4.1 Java Agent 原理

#### 4.1.1 Agent启动流程

```mermaid
sequenceDiagram
    participant JVM
    participant Agent
    participant Bootstrap
    participant Core
    
    JVM->>Agent: premain()调用
    Agent->>Bootstrap: 添加到Bootstrap ClassPath
    Agent->>Core: 创建隔离ClassLoader
    Core->>Core: 初始化Hook和Rule
    Core->>JVM: 注册ClassFileTransformer
    JVM->>Core: 类加载时回调transform()
```

#### 4.1.2 关键代码

```java
// RaspAgent.java - Agent入口
public static void premain(String agentArgs, Instrumentation inst) {
    try {
        // 1. 添加Agent JAR到Bootstrap ClassPath
        addAgentJarToBootstrapClassPath(inst);
        
        // 2. 验证Spy类可访问性
        verifySpy();
        
        // 3. 创建隔离ClassLoader并初始化RASP Core
        initializeRaspCore(inst);
        
    } catch (Exception e) {
        logger.error("RASP Agent failed to start", e);
    }
}
```

### 4.2 类加载器隔离原理

#### 4.2.1 隔离架构

```mermaid
graph TB
    A[Bootstrap ClassLoader] --> B[Spy类]
    C[Application ClassLoader] --> D[应用程序类]
    E[RaspClassLoader] --> F[RASP Core]
    E --> G[Hook实现]
    E --> H[Rule实现]

    B -.-> F
    F -.-> B
```

#### 4.2.2 隔离的必要性

1. **依赖冲突避免**：RASP使用的库版本与应用不冲突
2. **安全隔离**：防止应用程序干扰RASP功能
3. **内存隔离**：独立的类空间，避免内存泄漏

#### 4.2.3 RaspClassLoader实现

```java
public class RaspClassLoader extends URLClassLoader {
    private final String namespace;
    private final Map<String, Class<?>> classCache;

    @Override
    protected Class<?> loadClass(String name, boolean resolve)
            throws ClassNotFoundException {
        // 1. 检查缓存
        Class<?> clazz = classCache.get(name);
        if (clazz != null) {
            return clazz;
        }

        // 2. 系统类委托给父加载器
        if (isSystemClass(name)) {
            return super.loadClass(name, resolve);
        }

        // 3. RASP类自己加载
        if (isRaspClass(name)) {
            clazz = findClass(name);
            classCache.put(name, clazz);
            return clazz;
        }

        // 4. 其他类委托给父加载器
        return super.loadClass(name, resolve);
    }
}
```

### 4.3 字节码增强原理

#### 4.3.1 ASM字节码操作

```java
public class ClassEnhancer implements ClassFileTransformer {

    @Override
    public byte[] transform(ClassLoader loader, String className,
                          Class<?> classBeingRedefined,
                          ProtectionDomain protectionDomain,
                          byte[] classfileBuffer) {

        // 1. 检查是否需要增强
        if (!shouldEnhance(className)) {
            return null;
        }

        // 2. 使用ASM进行字节码增强
        ClassReader reader = new ClassReader(classfileBuffer);
        ClassWriter writer = new ClassWriter(ClassWriter.COMPUTE_FRAMES);
        ClassVisitor enhancer = new RaspClassVisitor(writer, className);

        reader.accept(enhancer, ClassReader.EXPAND_FRAMES);
        return writer.toByteArray();
    }
}
```

#### 4.3.2 方法增强示例

**原始方法**：
```java
public Process start() {
    return processImpl.start();
}
```

**增强后的方法**：
```java
public Process start() {
    // RASP插入的代码
    Spy.onMethodEnter("java.lang.ProcessBuilder", "start",
                      "()Ljava/lang/Process;", new Object[0]);

    // 原始代码
    return processImpl.start();
}
```

### 4.4 Hook机制原理

#### 4.4.1 Hook执行流程

```mermaid
sequenceDiagram
    participant App as 应用方法
    participant Spy as Spy类
    participant Hook as Hook实现
    participant Rule as Rule引擎

    App->>Spy: 方法调用触发
    Spy->>Hook: onMethodEnter()
    Hook->>Hook: 创建HookEvent
    Hook->>Rule: 发送事件到规则引擎
    Rule->>Rule: 执行安全检测
    Rule-->>Hook: 返回检测结果
    Hook-->>Spy: 返回处理结果
    Spy-->>App: 继续或阻断执行
```

#### 4.4.2 Hook实现示例

```java
@Component
public class CommandExecutionHook implements Hook {

    @Override
    public List<HookEvent> onMethodEnter(String className, String methodName,
                                       String signature, Object[] args) {

        // 1. 检查是否为目标方法
        if (!isTargetMethod(className, methodName)) {
            return Collections.emptyList();
        }

        // 2. 提取命令信息
        String command = extractCommand(args);

        // 3. 创建事件
        CommandExecutionEvent event = new CommandExecutionEvent(
            className, methodName, signature, command);

        // 4. 设置HTTP上下文
        event.setHttpRequest(RequestContext.getCurrentRequest());

        return Arrays.asList(event);
    }
}
```

### 4.5 HTTP请求数据获取原理

#### 4.5.1 请求上下文管理

```java
public class RequestContext {
    private static final ThreadLocal<HttpRequestInfo> REQUEST_CONTEXT =
        new ThreadLocal<>();

    public static void setCurrentRequest(HttpRequestInfo request) {
        REQUEST_CONTEXT.set(request);
    }

    public static HttpRequestInfo getCurrentRequest() {
        return REQUEST_CONTEXT.get();
    }

    public static void clearCurrentRequest() {
        REQUEST_CONTEXT.remove();
    }
}
```

#### 4.5.2 HTTP数据提取

```java
public class HttpRequestHook implements Hook {

    @Override
    public List<HookEvent> onMethodEnter(String className, String methodName,
                                       String signature, Object[] args) {

        // 1. 从方法参数中提取HttpServletRequest
        HttpServletRequest request = extractRequest(args);

        // 2. 构建HTTP请求信息
        HttpRequestInfo requestInfo = HttpRequestInfo.builder()
            .url(getFullURL(request))
            .method(request.getMethod())
            .path(request.getRequestURI())
            .queryString(request.getQueryString())
            .headers(extractHeaders(request))
            .parameters(extractParameters(request))
            .body(extractBody(request))
            .clientIp(getClientIP(request))
            .userAgent(request.getHeader("User-Agent"))
            .build();

        // 3. 设置到线程上下文
        RequestContext.setCurrentRequest(requestInfo);

        // 4. 创建HTTP事件
        return Arrays.asList(new HttpRequestEvent(requestInfo));
    }
}
```

### 4.6 安全检测和阻断原理

#### 4.6.1 Rule执行机制

```java
public class RuleEngine {

    public List<RuleResult> processEvent(HookEvent event) {
        List<RuleResult> results = new ArrayList<>();

        // 1. 找到适用的规则
        List<Rule> applicableRules = findApplicableRules(event.getEventType());

        // 2. 按优先级排序
        applicableRules.sort(Comparator.comparing(Rule::getPriority));

        // 3. 执行规则检测
        for (Rule rule : applicableRules) {
            try {
                RuleResult result = rule.evaluate(event);
                if (result.isMatched()) {
                    results.add(result);

                    // 4. 检查是否需要阻断
                    if (result.getAction() == RuleAction.BLOCK) {
                        break; // 遇到阻断规则，停止后续检测
                    }
                }
            } catch (Exception e) {
                logger.error("Rule execution failed: " + rule.getName(), e);
            }
        }

        return results;
    }
}
```

#### 4.6.2 命令注入检测示例

```java
public class CommandExecutionRule implements Rule {

    @Override
    public RuleResult evaluate(HookEvent event) {
        CommandExecutionEvent cmdEvent = (CommandExecutionEvent) event;
        String command = cmdEvent.getCommand();

        // 1. 获取HTTP上下文
        HttpRequestInfo httpRequest = cmdEvent.getHttpRequest();
        if (httpRequest == null) {
            return RuleResult.noMatch();
        }

        // 2. 检查命令是否来自HTTP参数
        if (isCommandFromHttpRequest(command, httpRequest)) {

            // 3. 评估风险等级
            RiskLevel risk = assessRiskLevel(command);

            // 4. 根据风险等级决定动作
            RuleAction action = (risk == RiskLevel.HIGH) ?
                RuleAction.BLOCK : RuleAction.ALERT;

            return RuleResult.builder()
                .matched(true)
                .action(action)
                .riskLevel(risk)
                .message("Potential command injection: '" + command +
                        "' from HTTP request")
                .build();
        }

        return RuleResult.noMatch();
    }

    private boolean isCommandFromHttpRequest(String command,
                                           HttpRequestInfo request) {
        // 检查查询参数
        if (containsCommand(request.getQueryString(), command)) {
            return true;
        }

        // 检查POST参数
        for (String value : request.getParameters().values()) {
            if (containsCommand(value, command)) {
                return true;
            }
        }

        // 检查请求体
        if (containsCommand(request.getRequestBody(), command)) {
            return true;
        }

        return false;
    }
}
```

### 4.7 自动注册机制原理

#### 4.7.1 包扫描实现

```java
public class RaspCore {

    private static void autoRegisterHooks(HookManager hookManager) {
        String[] hookPackages = {
            "com.rasp.hooks.http",
            "com.rasp.hooks.command",
            "com.rasp.hooks.file",
            "com.rasp.hooks.sql",
            "com.rasp.hooks.network"
        };

        for (String packageName : hookPackages) {
            scanAndRegisterHooks(packageName, hookManager);
        }
    }

    private static void scanAndRegisterHooks(String packageName,
                                           HookManager hookManager) {
        try {
            // 1. 获取包下的所有类
            String[] classNames = getClassNamesInPackage(packageName);

            for (String className : classNames) {
                Class<?> clazz = Class.forName(className);

                // 2. 检查是否实现了Hook接口
                if (Hook.class.isAssignableFrom(clazz) &&
                    !clazz.isInterface() &&
                    !Modifier.isAbstract(clazz.getModifiers())) {

                    // 3. 创建实例并注册
                    Hook hookInstance = (Hook) clazz.newInstance();
                    hookManager.registerHook(hookInstance);
                    logger.info("Auto-registered Hook: " + clazz.getSimpleName());
                }
            }
        } catch (Exception e) {
            logger.warn("Failed to scan Hook package: " + packageName, e);
        }
    }
}
```

---

## 5. 开发指南

### 5.1 开发环境搭建

#### 5.1.1 环境要求

- **JDK**: 8或更高版本
- **Maven**: 3.6或更高版本
- **IDE**: IntelliJ IDEA或Eclipse

#### 5.1.2 项目构建

```bash
# 克隆项目
git clone <repository-url>
cd rasp-agent

# 编译项目
mvn clean compile

# 打包项目
mvn clean package -DskipTests

# 运行测试
mvn test
```

### 5.2 新增Hook开发

#### 5.2.1 Hook开发步骤

1. **创建Hook类**
2. **实现Hook接口**
3. **放置到指定包路径**
4. **测试验证**

#### 5.2.2 Hook开发示例

**步骤1：创建文件操作Hook**

```java
// 文件路径：rasp-hooks/src/main/java/com/rasp/hooks/file/FileAccessHook.java
package com.rasp.hooks.file;

import com.rasp.api.hook.Hook;
import com.rasp.api.event.HookEvent;
import com.rasp.api.event.FileAccessEvent;

public class FileAccessHook implements Hook {

    @Override
    public String getName() {
        return "FileAccessHook";
    }

    @Override
    public String getDescription() {
        return "Hook for monitoring file access operations";
    }

    @Override
    public HookEvent.EventType[] getSupportedEventTypes() {
        return new HookEvent.EventType[]{HookEvent.EventType.FILE_ACCESS};
    }

    @Override
    public List<HookEvent> onMethodEnter(String className, String methodName,
                                       String signature, Object[] args) {

        // 检查是否为文件操作方法
        if (!isFileOperation(className, methodName)) {
            return Collections.emptyList();
        }

        // 提取文件路径
        String filePath = extractFilePath(args);

        // 创建文件访问事件
        FileAccessEvent event = new FileAccessEvent(
            className, methodName, signature, filePath);

        // 设置HTTP上下文
        event.setHttpRequest(RequestContext.getCurrentRequest());

        return Arrays.asList(event);
    }

    private boolean isFileOperation(String className, String methodName) {
        return (className.equals("java.io.FileInputStream") &&
                methodName.equals("<init>")) ||
               (className.equals("java.io.FileOutputStream") &&
                methodName.equals("<init>")) ||
               (className.equals("java.nio.file.Files") &&
                methodName.equals("readAllBytes"));
    }

    private String extractFilePath(Object[] args) {
        if (args.length > 0 && args[0] instanceof String) {
            return (String) args[0];
        }
        if (args.length > 0 && args[0] instanceof java.io.File) {
            return ((java.io.File) args[0]).getAbsolutePath();
        }
        return "unknown";
    }
}
```

**步骤2：创建对应的Event类**

```java
// 文件路径：rasp-api/src/main/java/com/rasp/api/event/FileAccessEvent.java
package com.rasp.api.event;

public class FileAccessEvent extends HookEvent {
    private final String filePath;

    public FileAccessEvent(String className, String methodName,
                          String signature, String filePath) {
        super(EventType.FILE_ACCESS, className, methodName, signature);
        this.filePath = filePath;
    }

    public String getFilePath() {
        return filePath;
    }

    @Override
    public String toString() {
        return String.format("FileAccessEvent{filePath='%s', %s}",
                           filePath, super.toString());
    }
}
```

**步骤3：系统自动注册**

将Hook类放在`com.rasp.hooks.file`包下，系统会自动发现并注册。

### 5.3 新增Rule开发

#### 5.3.1 Rule开发示例

```java
// 文件路径：rasp-rules/src/main/java/com/rasp/rules/security/FileAccessRule.java
package com.rasp.rules.security;

import com.rasp.api.rule.Rule;
import com.rasp.api.rule.RuleResult;
import com.rasp.api.event.HookEvent;
import com.rasp.api.event.FileAccessEvent;

public class FileAccessRule implements Rule {

    // 敏感文件路径列表
    private static final String[] SENSITIVE_PATHS = {
        "/etc/passwd", "/etc/shadow", "C:\\Windows\\System32\\config\\SAM",
        ".ssh/id_rsa", ".aws/credentials"
    };

    @Override
    public String getName() {
        return "FileAccessRule";
    }

    @Override
    public String getDescription() {
        return "Detect unauthorized access to sensitive files";
    }

    @Override
    public HookEvent.EventType[] getSupportedEventTypes() {
        return new HookEvent.EventType[]{HookEvent.EventType.FILE_ACCESS};
    }

    @Override
    public int getPriority() {
        return 10; // 高优先级
    }

    @Override
    public RuleResult evaluate(HookEvent event) {
        FileAccessEvent fileEvent = (FileAccessEvent) event;
        String filePath = fileEvent.getFilePath();

        // 检查是否访问敏感文件
        if (isSensitiveFile(filePath)) {

            // 检查是否来自HTTP请求
            if (fileEvent.getHttpRequest() != null) {
                return RuleResult.builder()
                    .matched(true)
                    .action(RuleAction.BLOCK)
                    .riskLevel(RiskLevel.HIGH)
                    .ruleName(getName())
                    .message("Unauthorized access to sensitive file: " + filePath)
                    .build();
            } else {
                return RuleResult.builder()
                    .matched(true)
                    .action(RuleAction.ALERT)
                    .riskLevel(RiskLevel.MEDIUM)
                    .ruleName(getName())
                    .message("Access to sensitive file: " + filePath)
                    .build();
            }
        }

        return RuleResult.noMatch();
    }

    private boolean isSensitiveFile(String filePath) {
        for (String sensitivePath : SENSITIVE_PATHS) {
            if (filePath.contains(sensitivePath)) {
                return true;
            }
        }
        return false;
    }
}
```

### 5.4 配置Hook目标方法

#### 5.4.1 在ClassEnhancer中添加目标类

```java
// rasp-core/src/main/java/com/rasp/core/enhance/ClassEnhancer.java
private boolean shouldEnhance(String className) {
    return className.equals("java/lang/ProcessBuilder") ||
           className.equals("java/lang/Runtime") ||
           className.equals("javax/servlet/http/HttpServlet") ||
           className.equals("java/io/FileInputStream") ||        // 新增
           className.equals("java/io/FileOutputStream") ||       // 新增
           className.equals("java/nio/file/Files");              // 新增
}
```

#### 5.4.2 在HookManager中注册目标方法

```java
// rasp-core/src/main/java/com/rasp/core/hook/HookManager.java
private void initializeHookMethods() {
    // 现有的Hook方法...

    // 文件操作Hook
    addHookMethod("java.io.FileInputStream", "<init>",
                  "(Ljava/lang/String;)V");
    addHookMethod("java.io.FileOutputStream", "<init>",
                  "(Ljava/lang/String;)V");
    addHookMethod("java.nio.file.Files", "readAllBytes",
                  "(Ljava/nio/file/Path;)[B");
}
```

---

## 6. 部署运行

### 6.1 构建部署包

```bash
# 完整构建
mvn clean package -DskipTests

# 构建结果
ls -la rasp-agent-bootstrap/target/
# rasp-agent-bootstrap-1.0.0.jar  <- 这是需要的Agent JAR
```

### 6.2 运行方式

#### 6.2.1 基本运行

```bash
java -javaagent:rasp-agent-bootstrap-1.0.0.jar -jar your-application.jar
```

#### 6.2.2 带参数运行

```bash
java -javaagent:rasp-agent-bootstrap-1.0.0.jar=config=rasp.properties \
     -Drasp.log.level=INFO \
     -jar your-application.jar
```

#### 6.2.3 生产环境运行

```bash
java -server \
     -Xms2g -Xmx4g \
     -javaagent:rasp-agent-bootstrap-1.0.0.jar \
     -Drasp.log.level=WARN \
     -Drasp.home=/opt/rasp \
     -jar your-application.jar
```

### 6.3 配置文件

#### 6.3.1 日志配置 (logback.xml)

```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 文件输出 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/rasp-agent.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/rasp-agent.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- RASP相关包的日志级别 -->
    <logger name="com.rasp" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </logger>

    <!-- 根日志级别 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </root>
</configuration>
```

#### 6.3.2 RASP配置文件 (rasp.properties)

```properties
# RASP基础配置
rasp.enabled=true
rasp.log.level=INFO
rasp.log.file=logs/rasp-agent.log

# Hook配置
rasp.hooks.enabled=true
rasp.hooks.command.enabled=true
rasp.hooks.http.enabled=true
rasp.hooks.file.enabled=true

# Rule配置
rasp.rules.enabled=true
rasp.rules.command.block=true
rasp.rules.file.block=true

# 性能配置
rasp.performance.max_events_per_second=1000
rasp.performance.max_rule_execution_time=100ms

# 白名单配置
rasp.whitelist.commands=ps,ls,pwd,whoami
rasp.whitelist.files=/tmp/*,/var/log/*
rasp.whitelist.ips=127.0.0.1,::1
```

### 6.4 测试验证

#### 6.4.1 使用测试应用

```bash
# 启动测试应用
java -javaagent:rasp-agent-bootstrap-1.0.0.jar \
     -jar vuln-test/vuln-springboot2-3.0.3.jar

# 测试命令注入
curl "http://localhost:8002/CMD/get/cmd.do?cmd=whoami"

# 测试文件访问
curl "http://localhost:8002/file/read?path=/etc/passwd"
```

#### 6.4.2 预期输出

```
16:08:23.760 [main] INFO  com.rasp.core.RaspCore - Auto-registered Hook: HttpRequestHook
16:08:23.765 [main] INFO  com.rasp.core.RaspCore - Auto-registered Hook: CommandExecutionHook
16:08:23.774 [main] INFO  com.rasp.core.RaspCore - Auto-registered Rule: CommandExecutionRule
16:08:23.775 [main] INFO  com.rasp.core.RaspCore - Auto-registered Rule: HttpRequestLogRule

[CommandExecutionRule] POTENTIAL COMMAND INJECTION DETECTED!
Command 'whoami' appears to come from HTTP request parameters
```

---

## 7. 故障排查

### 7.1 常见问题

#### 7.1.1 Agent启动失败

**问题现象**：
```
Error occurred during initialization of VM
Could not find agent library rasp-agent-bootstrap-1.0.0.jar
```

**解决方案**：
1. 检查JAR文件路径是否正确
2. 确认JAR文件存在且可读
3. 使用绝对路径指定Agent JAR

#### 7.1.2 类加载冲突

**问题现象**：
```
java.lang.LinkageError: loader constraint violation
```

**解决方案**：
1. 检查RaspClassLoader的类加载策略
2. 确认系统类和RASP类的边界定义
3. 调整类加载器的委托机制

#### 7.1.3 Hook不生效

**问题现象**：
- Hook注册成功但方法调用时不触发
- 日志显示Hook已注册但无事件产生

**排查步骤**：
```java
// 1. 检查目标类是否被正确增强
logger.info("Enhancing class: " + className);

// 2. 检查方法签名是否匹配
logger.info("Target method: " + methodName + signature);

// 3. 检查Spy回调是否正常
logger.info("Spy callback triggered for: " + className + "." + methodName);
```

#### 7.1.4 Rule执行异常

**问题现象**：
```
Rule execution failed: CommandExecutionRule
java.lang.NullPointerException
```

**解决方案**：
1. 检查Rule中的空指针处理
2. 验证事件数据的完整性
3. 添加异常处理和日志记录

### 7.2 调试技巧

#### 7.2.1 启用详细日志

```bash
java -javaagent:rasp-agent-bootstrap-1.0.0.jar \
     -Drasp.log.level=DEBUG \
     -Drasp.debug.enabled=true \
     -jar your-application.jar
```

#### 7.2.2 Hook调试

```java
public class DebugHook implements Hook {
    @Override
    public List<HookEvent> onMethodEnter(String className, String methodName,
                                       String signature, Object[] args) {
        // 详细日志记录
        logger.debug("Hook triggered: {}#{}{}", className, methodName, signature);
        logger.debug("Arguments: {}", Arrays.toString(args));
        logger.debug("Thread: {}", Thread.currentThread().getName());
        logger.debug("Stack trace: ", new Exception("Debug trace"));

        return Collections.emptyList();
    }
}
```

#### 7.2.3 性能监控

```java
public class PerformanceMonitor {
    private static final Map<String, Long> methodTimes = new ConcurrentHashMap<>();

    public static void recordMethodTime(String method, long timeMs) {
        methodTimes.put(method, timeMs);
        if (timeMs > 100) { // 超过100ms的方法
            logger.warn("Slow method detected: {} took {}ms", method, timeMs);
        }
    }
}
```

---

## 8. 扩展开发

### 8.1 高级Hook开发

#### 8.1.1 异步Hook处理

```java
public class AsyncHook implements Hook {
    private final ExecutorService executor = Executors.newFixedThreadPool(4);

    @Override
    public List<HookEvent> onMethodEnter(String className, String methodName,
                                       String signature, Object[] args) {

        // 创建事件
        HookEvent event = createEvent(className, methodName, signature, args);

        // 异步处理
        executor.submit(() -> {
            try {
                processEventAsync(event);
            } catch (Exception e) {
                logger.error("Async hook processing failed", e);
            }
        });

        return Arrays.asList(event);
    }

    private void processEventAsync(HookEvent event) {
        // 执行耗时的处理逻辑
        // 例如：外部API调用、复杂计算等
    }
}
```

#### 8.1.2 条件Hook

```java
public class ConditionalHook implements Hook {

    @Override
    public List<HookEvent> onMethodEnter(String className, String methodName,
                                       String signature, Object[] args) {

        // 条件1：只在特定时间段内生效
        if (!isActiveTimeWindow()) {
            return Collections.emptyList();
        }

        // 条件2：只对特定用户生效
        if (!isTargetUser()) {
            return Collections.emptyList();
        }

        // 条件3：基于配置的动态开关
        if (!isHookEnabled(className, methodName)) {
            return Collections.emptyList();
        }

        return createEvents(className, methodName, signature, args);
    }

    private boolean isActiveTimeWindow() {
        LocalTime now = LocalTime.now();
        return now.isAfter(LocalTime.of(9, 0)) &&
               now.isBefore(LocalTime.of(18, 0));
    }
}
```

### 8.2 高级Rule开发

#### 8.2.1 机器学习Rule

```java
public class MLBasedRule implements Rule {
    private final MLModel model;

    public MLBasedRule() {
        this.model = loadModel("command_injection_model.pkl");
    }

    @Override
    public RuleResult evaluate(HookEvent event) {
        CommandExecutionEvent cmdEvent = (CommandExecutionEvent) event;

        // 特征提取
        double[] features = extractFeatures(cmdEvent);

        // 模型预测
        double riskScore = model.predict(features);

        // 基于风险分数决定动作
        if (riskScore > 0.8) {
            return RuleResult.builder()
                .matched(true)
                .action(RuleAction.BLOCK)
                .riskLevel(RiskLevel.HIGH)
                .confidence(riskScore)
                .message("ML model detected high risk: " + riskScore)
                .build();
        } else if (riskScore > 0.5) {
            return RuleResult.builder()
                .matched(true)
                .action(RuleAction.ALERT)
                .riskLevel(RiskLevel.MEDIUM)
                .confidence(riskScore)
                .message("ML model detected medium risk: " + riskScore)
                .build();
        }

        return RuleResult.noMatch();
    }

    private double[] extractFeatures(CommandExecutionEvent event) {
        String command = event.getCommand();
        HttpRequestInfo request = event.getHttpRequest();

        return new double[] {
            command.length(),                           // 命令长度
            countSpecialChars(command),                // 特殊字符数量
            request != null ? 1.0 : 0.0,              // 是否来自HTTP请求
            calculateEntropy(command),                  // 命令熵值
            isKnownMaliciousPattern(command) ? 1.0 : 0.0 // 已知恶意模式
        };
    }
}
```

#### 8.2.2 规则链

```java
public class RuleChain implements Rule {
    private final List<Rule> rules;
    private final ChainMode mode;

    public enum ChainMode {
        ALL_MUST_MATCH,    // 所有规则都必须匹配
        ANY_CAN_MATCH,     // 任一规则匹配即可
        WEIGHTED_SCORE     // 加权评分
    }

    @Override
    public RuleResult evaluate(HookEvent event) {
        List<RuleResult> results = new ArrayList<>();

        // 执行所有子规则
        for (Rule rule : rules) {
            RuleResult result = rule.evaluate(event);
            if (result.isMatched()) {
                results.add(result);
            }
        }

        // 根据模式合并结果
        return mergeResults(results);
    }

    private RuleResult mergeResults(List<RuleResult> results) {
        switch (mode) {
            case ALL_MUST_MATCH:
                return results.size() == rules.size() ?
                    combineResults(results) : RuleResult.noMatch();

            case ANY_CAN_MATCH:
                return results.isEmpty() ?
                    RuleResult.noMatch() : results.get(0);

            case WEIGHTED_SCORE:
                return calculateWeightedResult(results);

            default:
                return RuleResult.noMatch();
        }
    }
}
```

### 8.3 自定义事件类型

#### 8.3.1 定义新事件类型

```java
// 在HookEvent.EventType中添加新类型
public enum EventType {
    HTTP_REQUEST,
    COMMAND_EXECUTION,
    FILE_ACCESS,
    SQL_EXECUTION,
    NETWORK_CONNECTION,    // 新增
    CRYPTO_OPERATION,      // 新增
    REFLECTION_CALL        // 新增
}
```

#### 8.3.2 实现对应的事件类

```java
public class NetworkConnectionEvent extends HookEvent {
    private final String targetHost;
    private final int targetPort;
    private final String protocol;

    public NetworkConnectionEvent(String className, String methodName,
                                String signature, String targetHost,
                                int targetPort, String protocol) {
        super(EventType.NETWORK_CONNECTION, className, methodName, signature);
        this.targetHost = targetHost;
        this.targetPort = targetPort;
        this.protocol = protocol;
    }

    // Getters...
}
```

### 8.4 插件化架构

#### 8.4.1 插件接口定义

```java
public interface RaspPlugin {
    String getName();
    String getVersion();
    String getDescription();

    void initialize(PluginContext context);
    void destroy();

    List<Hook> getHooks();
    List<Rule> getRules();
}
```

#### 8.4.2 插件加载器

```java
public class PluginLoader {
    private final Map<String, RaspPlugin> loadedPlugins = new HashMap<>();

    public void loadPlugin(String pluginPath) {
        try {
            // 1. 创建插件类加载器
            URLClassLoader pluginClassLoader = new URLClassLoader(
                new URL[]{new File(pluginPath).toURI().toURL()},
                this.getClass().getClassLoader()
            );

            // 2. 加载插件主类
            Class<?> pluginClass = pluginClassLoader.loadClass("PluginMain");
            RaspPlugin plugin = (RaspPlugin) pluginClass.newInstance();

            // 3. 初始化插件
            plugin.initialize(createPluginContext());

            // 4. 注册Hook和Rule
            registerPluginComponents(plugin);

            loadedPlugins.put(plugin.getName(), plugin);
            logger.info("Plugin loaded: " + plugin.getName());

        } catch (Exception e) {
            logger.error("Failed to load plugin: " + pluginPath, e);
        }
    }
}
```

### 8.5 性能优化

#### 8.5.1 事件过滤器

```java
public class EventFilter {
    private final Set<String> ignoredClasses = new HashSet<>();
    private final Map<String, Integer> rateLimits = new HashMap<>();

    public boolean shouldProcess(HookEvent event) {
        // 1. 类名过滤
        if (ignoredClasses.contains(event.getClassName())) {
            return false;
        }

        // 2. 频率限制
        String key = event.getClassName() + "#" + event.getMethodName();
        Integer limit = rateLimits.get(key);
        if (limit != null && exceedsRateLimit(key, limit)) {
            return false;
        }

        // 3. 时间窗口过滤
        if (!isInActiveWindow(event.getTimestamp())) {
            return false;
        }

        return true;
    }
}
```

#### 8.5.2 缓存机制

```java
public class RuleResultCache {
    private final Cache<String, RuleResult> cache = CacheBuilder.newBuilder()
        .maximumSize(10000)
        .expireAfterWrite(5, TimeUnit.MINUTES)
        .build();

    public RuleResult getCachedResult(String eventKey, Rule rule) {
        return cache.getIfPresent(eventKey + "#" + rule.getName());
    }

    public void cacheResult(String eventKey, Rule rule, RuleResult result) {
        if (result.isCacheable()) {
            cache.put(eventKey + "#" + rule.getName(), result);
        }
    }
}
```

---

## 9. 最佳实践

### 9.1 开发最佳实践

#### 9.1.1 Hook开发原则

1. **最小化性能影响**：Hook代码应尽可能轻量
2. **异常安全**：确保Hook异常不影响应用程序
3. **线程安全**：考虑多线程环境下的数据安全
4. **资源管理**：及时释放资源，避免内存泄漏

#### 9.1.2 Rule开发原则

1. **快速执行**：Rule应在毫秒级完成检测
2. **准确性优先**：减少误报和漏报
3. **可配置性**：支持动态配置和调整
4. **可观测性**：提供详细的执行日志

### 9.2 部署最佳实践

#### 9.2.1 生产环境配置

```bash
# JVM参数优化
-server
-Xms2g -Xmx4g
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+PrintGCDetails
-XX:+PrintGCTimeStamps

# RASP参数
-javaagent:rasp-agent-bootstrap-1.0.0.jar
-Drasp.log.level=WARN
-Drasp.performance.enabled=true
-Drasp.cache.enabled=true
```

#### 9.2.2 监控和告警

```yaml
# Prometheus监控指标
rasp_events_total{type="command_execution"}
rasp_rules_execution_time{rule="CommandExecutionRule"}
rasp_blocks_total{reason="command_injection"}
rasp_errors_total{component="hook_manager"}
```

### 9.3 安全最佳实践

#### 9.3.1 配置安全

1. **最小权限原则**：只启用必要的Hook和Rule
2. **白名单机制**：对可信操作建立白名单
3. **加密配置**：敏感配置信息加密存储
4. **访问控制**：限制配置文件的访问权限

#### 9.3.2 运行时安全

1. **隔离保护**：确保RASP组件与应用隔离
2. **完整性检查**：验证Hook和Rule的完整性
3. **审计日志**：记录所有安全事件和配置变更
4. **应急响应**：建立安全事件的应急处理流程

---

## 10. 总结

### 10.1 技术特点

RASP Agent项目具有以下技术特点：

1. **零侵入性**：基于Java Agent技术，无需修改应用代码
2. **高性能**：优化的字节码增强和事件处理机制
3. **可扩展性**：插件化架构，支持自定义Hook和Rule
4. **隔离性**：独立的类加载器，避免依赖冲突
5. **实时性**：运行时检测，即时响应安全威胁

### 10.2 应用场景

- **Web应用安全**：防护Web应用的各类注入攻击
- **API安全**：保护REST API和微服务安全
- **容器安全**：在容器化环境中提供运行时保护
- **云原生安全**：适配云原生应用的安全需求

### 10.3 发展方向

1. **AI增强**：集成机器学习算法提升检测准确性
2. **云原生**：支持Kubernetes等云原生平台
3. **可视化**：提供Web控制台和监控面板
4. **标准化**：支持OWASP等安全标准

---

## 附录

### A. 常用命令

```bash
# 构建项目
mvn clean package -DskipTests

# 运行测试
mvn test

# 启动测试应用
java -javaagent:rasp-agent-bootstrap-1.0.0.jar -jar vuln-test.jar

# 查看日志
tail -f logs/rasp-agent.log

# 性能分析
jstack <pid>
jmap -dump:format=b,file=heap.hprof <pid>
```

### B. 配置参考

```properties
# 完整配置示例
rasp.enabled=true
rasp.log.level=INFO
rasp.log.file=logs/rasp-agent.log
rasp.log.max_size=100MB
rasp.log.max_files=10

rasp.hooks.enabled=true
rasp.hooks.http.enabled=true
rasp.hooks.command.enabled=true
rasp.hooks.file.enabled=true
rasp.hooks.sql.enabled=true

rasp.rules.enabled=true
rasp.rules.command.block=true
rasp.rules.file.block=false
rasp.rules.sql.block=true

rasp.performance.max_events_per_second=1000
rasp.performance.max_rule_execution_time=100
rasp.performance.cache_enabled=true
rasp.performance.cache_size=10000

rasp.whitelist.commands=ps,ls,pwd,whoami,date
rasp.whitelist.files=/tmp/*,/var/log/*,/opt/app/logs/*
rasp.whitelist.ips=127.0.0.1,::1,10.0.0.0/8
```

### C. API参考

详细的API文档请参考各模块的JavaDoc注释。

---

**文档版本**: 1.0.0
**最后更新**: 2025-06-25
**作者**: RASP开发团队
```
```
```
```
```
```
```
