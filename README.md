# RASP Agent

Runtime Application Self-Protection (RASP) Agent for Java applications.

## 项目结构

```
rasp-agent/
├── rasp-api/                   # API定义模块
│   └── src/main/java/com/rasp/api/
│       ├── event/              # 事件定义
│       ├── hook/               # Hook接口
│       └── rule/               # 规则接口
├── rasp-core/                  # 核心引擎模块
│   └── src/main/java/com/rasp/core/
│       ├── classloader/        # 类加载器
│       ├── enhance/            # 字节码增强
│       ├── hook/               # Hook管理
│       ├── rule/               # 规则引擎
│       └── spy/                # Spy机制
├── rasp-hooks/                 # Hook实现模块
│   └── src/main/java/com/rasp/hooks/
│       └── http/               # HTTP相关Hook
├── rasp-rules/                 # 规则实现模块
│   └── src/main/java/com/rasp/rules/
│       └── demo/               # 示例规则
└── rasp-agent-bootstrap/       # Agent启动模块
    └── src/main/java/com/rasp/agent/
```

## 核心特性

1. **类隔离机制**: 参考jvm-sandbox设计，通过自定义ClassLoader实现RASP与目标应用的类隔离
2. **Hook系统**: 可扩展的Hook机制，支持HTTP请求、方法调用等多种Hook类型
3. **规则引擎**: 灵活的规则系统，支持动态加载和卸载规则
4. **字节码增强**: 使用ASM进行运行时字节码增强，实现无侵入式监控
5. **事件驱动**: 基于事件的架构，支持BEFORE、RETURN、THROWS三个阶段的处理

## 构建和使用

### 构建项目

```bash
# 使用构建脚本
chmod +x build.sh
./build.sh

# 或者直接使用Maven
mvn clean package
```

### 使用RASP Agent

1. **启动时加载** (推荐):
```bash
java -javaagent:rasp-agent-bootstrap/target/rasp-agent-bootstrap-1.0.0.jar -jar your-app.jar
```

2. **使用启动脚本**:
```bash
./start-with-rasp.sh java -jar your-app.jar
```

3. **测试靶场应用**:
```bash
# 进入靶场目录
cd ../javaweb-vuln-master

# 使用RASP启动靶场
../rasp-agent/start-with-rasp.sh mvn spring-boot:run
```

### 配置选项

可以通过系统属性配置RASP行为:

- `-Drasp.log.level=INFO`: 设置日志级别 (DEBUG, INFO, WARN, ERROR)
- `-Drasp.log.dir=./logs`: 设置日志目录
- `-Drasp.home=/path/to/rasp`: 设置RASP_HOME目录，用于加载外部模块

## 内置Hook和规则

### Hook类型

1. **HttpRequestHook**: 采集HTTP请求信息
   - 请求URL、方法、参数
   - 请求头、客户端IP
   - Session信息

### 规则类型

1. **HttpRequestLogRule**: 打印HTTP请求详细信息
2. **MethodCallLogRule**: 打印方法调用详细信息

## 扩展开发

### 开发自定义Hook

1. 继承 `AbstractHook` 类
2. 实现必要的方法:
   - `getName()`: Hook名称
   - `getClassNamePatterns()`: 要Hook的类名模式
   - `getMethodNamePatterns()`: 要Hook的方法名模式
   - `onMethodEnter()`: 方法进入时的处理逻辑

```java
public class CustomHook extends AbstractHook {
    @Override
    public String getName() {
        return "CustomHook";
    }
    
    @Override
    public String[] getClassNamePatterns() {
        return new String[]{"com.example.*"};
    }
    
    @Override
    public String[] getMethodNamePatterns() {
        return new String[]{"execute*"};
    }
    
    @Override
    public HookEvent onMethodEnter(String className, String methodName, 
                                  String methodSignature, Object target, Object[] arguments) {
        // 实现Hook逻辑
        return new HookEvent();
    }
}
```

### 开发自定义规则

1. 继承 `AbstractRule` 类
2. 实现必要的方法:
   - `getName()`: 规则名称
   - `getSupportedEventTypes()`: 支持的事件类型
   - `doProcess()`: 规则处理逻辑

```java
public class CustomRule extends AbstractRule {
    @Override
    public String getName() {
        return "CustomRule";
    }
    
    @Override
    public HookEvent.EventType[] getSupportedEventTypes() {
        return new HookEvent.EventType[]{HookEvent.EventType.HTTP_REQUEST};
    }
    
    @Override
    protected RuleResult doProcess(HookEvent event) {
        // 实现规则逻辑
        return RuleResult.allow(getName());
    }
}
```

## 架构设计

RASP Agent采用模块化设计，主要组件包括:

1. **类加载器层**: 实现类隔离，避免与目标应用冲突
2. **字节码增强层**: 使用ASM在运行时修改字节码
3. **Hook管理层**: 管理Hook的注册、匹配和调用
4. **规则引擎层**: 处理Hook事件，执行安全规则
5. **Spy通信层**: 作为增强代码与RASP内核的通信桥梁

## 注意事项

1. 确保使用JDK 8或更高版本
2. RASP Agent会对应用性能产生一定影响，建议在测试环境充分验证
3. 日志文件可能会快速增长，注意磁盘空间管理
4. 某些复杂的应用可能需要调整Hook规则以避免冲突

## 故障排除

1. **Agent启动失败**: 检查Java版本和JAR文件路径
2. **Hook不生效**: 检查类名和方法名模式是否正确
3. **性能问题**: 调整日志级别，减少不必要的Hook
4. **类加载冲突**: 检查类隔离配置
