package com.rasp.core.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;
import java.util.Set;
import java.util.HashSet;

/**
 * RASP配置管理器
 * 负责加载和管理RASP的配置信息，包括规则的启用/禁用状态
 */
public class RaspConfigManager {
    
    private static final Logger logger = LoggerFactory.getLogger(RaspConfigManager.class);
    
    /**
     * 单例实例
     */
    private static volatile RaspConfigManager instance;
    
    /**
     * 配置属性
     */
    private Properties properties;
    
    /**
     * 默认配置文件名
     */
    private static final String DEFAULT_CONFIG_FILE = "rasp-config.properties";
    
    /**
     * 规则配置前缀
     */
    private static final String RULE_PREFIX = "rasp.rule.";
    
    /**
     * 启用后缀
     */
    private static final String ENABLED_SUFFIX = ".enabled";
    
    /**
     * 私有构造函数
     */
    private RaspConfigManager() {
        loadConfiguration();
    }
    
    /**
     * 获取单例实例
     * @return 配置管理器实例
     */
    public static RaspConfigManager getInstance() {
        if (instance == null) {
            synchronized (RaspConfigManager.class) {
                if (instance == null) {
                    instance = new RaspConfigManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * 加载配置文件
     */
    private void loadConfiguration() {
        properties = new Properties();
        
        // 首先加载默认配置
        loadDefaultConfiguration();
        
        // 尝试从多个位置加载配置文件
        String[] configLocations = {
            System.getProperty("rasp.config.file"), // 系统属性指定的配置文件
            DEFAULT_CONFIG_FILE,                     // 类路径下的默认配置文件
            "config/" + DEFAULT_CONFIG_FILE,         // config目录下的配置文件
        };
        
        boolean configLoaded = false;
        for (String location : configLocations) {
            if (location != null && loadConfigurationFromLocation(location)) {
                configLoaded = true;
                logger.info("Configuration loaded from: {}", location);
                break;
            }
        }
        
        if (!configLoaded) {
            logger.warn("No configuration file found, using default configuration");
        }
        
        // 打印配置摘要
        logConfigurationSummary();
    }
    
    /**
     * 加载默认配置
     */
    private void loadDefaultConfiguration() {
        // 默认规则配置 - 大部分规则默认启用，日志类规则默认禁用
        properties.setProperty("rasp.rule.HttpRequestLogRule.enabled", "true");
        properties.setProperty("rasp.rule.CommandExecutionRule.enabled", "true");
        properties.setProperty("rasp.rule.FileWriteRule.enabled", "true");
        properties.setProperty("rasp.rule.MethodCallLogRule.enabled", "false"); // 日志规则默认禁用
        
        // 全局配置
        properties.setProperty("rasp.enabled", "true");
        properties.setProperty("rasp.log.level", "INFO");
        
        logger.info("Default configuration loaded");
    }
    
    /**
     * 从指定位置加载配置文件
     * @param location 配置文件位置
     * @return 是否成功加载
     */
    private boolean loadConfigurationFromLocation(String location) {
        try (InputStream inputStream = getConfigInputStream(location)) {
            if (inputStream != null) {
                properties.load(inputStream);
                return true;
            }
        } catch (IOException e) {
            logger.debug("Failed to load configuration from: {}, error: {}", location, e.getMessage());
        }
        return false;
    }
    
    /**
     * 获取配置文件输入流
     * @param location 配置文件位置
     * @return 输入流，如果文件不存在返回null
     */
    private InputStream getConfigInputStream(String location) {
        // 首先尝试从文件系统加载
        try {
            java.io.File file = new java.io.File(location);
            if (file.exists() && file.isFile()) {
                return new java.io.FileInputStream(file);
            }
        } catch (Exception e) {
            logger.debug("Failed to load from file system: {}", location);
        }
        
        // 然后尝试从类路径加载
        return getClass().getClassLoader().getResourceAsStream(location);
    }
    
    /**
     * 打印配置摘要
     */
    private void logConfigurationSummary() {
        logger.info("=== RASP Configuration Summary ===");
        logger.info("RASP Enabled: {}", getProperty("rasp.enabled", "true"));
        logger.info("Log Level: {}", getProperty("rasp.log.level", "INFO"));
        
        // 打印规则配置
        logger.info("=== Rule Configuration ===");
        Set<String> ruleNames = getRuleNames();
        for (String ruleName : ruleNames) {
            boolean enabled = isRuleEnabled(ruleName);
            logger.info("Rule {}: {}", ruleName, enabled ? "ENABLED" : "DISABLED");
        }
        logger.info("=== End Configuration Summary ===");
    }
    
    /**
     * 获取所有配置的规则名称
     * @return 规则名称集合
     */
    private Set<String> getRuleNames() {
        Set<String> ruleNames = new HashSet<>();
        for (String key : properties.stringPropertyNames()) {
            if (key.startsWith(RULE_PREFIX) && key.endsWith(ENABLED_SUFFIX)) {
                // 提取规则名称：rasp.rule.RuleName.enabled -> RuleName
                String ruleName = key.substring(RULE_PREFIX.length(), key.length() - ENABLED_SUFFIX.length());
                ruleNames.add(ruleName);
            }
        }
        return ruleNames;
    }
    
    /**
     * 检查指定规则是否启用
     * @param ruleName 规则名称
     * @return true表示启用，false表示禁用
     */
    public boolean isRuleEnabled(String ruleName) {
        String key = RULE_PREFIX + ruleName + ENABLED_SUFFIX;
        String value = properties.getProperty(key);
        
        if (value == null) {
            // 如果配置文件中没有指定，默认启用（除了已知的日志规则）
            if (ruleName.contains("Log")) {
                logger.debug("Rule {} not configured, defaulting to DISABLED (log rule)", ruleName);
                return false;
            } else {
                logger.debug("Rule {} not configured, defaulting to ENABLED", ruleName);
                return true;
            }
        }
        
        return Boolean.parseBoolean(value);
    }
    
    /**
     * 获取配置属性值
     * @param key 属性键
     * @param defaultValue 默认值
     * @return 属性值
     */
    public String getProperty(String key, String defaultValue) {
        return properties.getProperty(key, defaultValue);
    }
    
    /**
     * 获取配置属性值
     * @param key 属性键
     * @return 属性值，如果不存在返回null
     */
    public String getProperty(String key) {
        return properties.getProperty(key);
    }
    
    /**
     * 获取布尔类型配置值
     * @param key 属性键
     * @param defaultValue 默认值
     * @return 布尔值
     */
    public boolean getBooleanProperty(String key, boolean defaultValue) {
        String value = properties.getProperty(key);
        if (value == null) {
            return defaultValue;
        }
        return Boolean.parseBoolean(value);
    }
    
    /**
     * 获取整数类型配置值
     * @param key 属性键
     * @param defaultValue 默认值
     * @return 整数值
     */
    public int getIntProperty(String key, int defaultValue) {
        String value = properties.getProperty(key);
        if (value == null) {
            return defaultValue;
        }
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            logger.warn("Invalid integer value for key {}: {}, using default: {}", key, value, defaultValue);
            return defaultValue;
        }
    }
    
    /**
     * 检查RASP是否启用
     * @return true表示启用
     */
    public boolean isRaspEnabled() {
        return getBooleanProperty("rasp.enabled", true);
    }
    
    /**
     * 重新加载配置
     */
    public void reload() {
        logger.info("Reloading RASP configuration...");
        loadConfiguration();
    }
}
