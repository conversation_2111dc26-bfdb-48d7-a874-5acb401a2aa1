# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# IDE
.idea/
*.iws
*.iml
*.ipr
.vscode/
.settings/
.project
.classpath

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Test files
vuln-test/logs/
vuln-test/BOOT-INF/

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~

# Java
*.class
*.jar
!vuln-test/vuln-springboot2-3.0.3.jar
