package com.rasp.core.spy;

/**
 * Spy回调实现类
 * 放在Bootstrap模块中，避免跨类加载器问题
 * 直接作为字节码插桩的入口点
 */
public class SpyCallbackImpl {
    
    // 使用反射调用隔离类加载器中的实际处理器
    private static volatile Object actualHandler;
    private static volatile java.lang.reflect.Method onMethodEnterMethod;
    private static volatile java.lang.reflect.Method onMethodReturnMethod;
    private static volatile java.lang.reflect.Method onMethodThrowMethod;
    private static volatile java.lang.reflect.Method onMethodExitMethod;
    
    /**
     * 设置实际的处理器（由RASP核心调用）
     */
    public static void setActualHandler(Object handler) {
        try {
            actualHandler = handler;
            Class<?> handlerClass = handler.getClass();
            
            // 缓存反射方法并设置可访问性
            onMethodEnterMethod = handlerClass.getMethod("handleOnMethodEnter",
                String.class, String.class, String.class, Object.class, Object[].class);
            onMethodEnterMethod.setAccessible(true);

            onMethodReturnMethod = handlerClass.getMethod("handleOnMethodReturn",
                Object[].class, Object.class);
            onMethodReturnMethod.setAccessible(true);

            onMethodThrowMethod = handlerClass.getMethod("handleOnMethodThrow",
                Object[].class, Throwable.class);
            onMethodThrowMethod.setAccessible(true);

            onMethodExitMethod = handlerClass.getMethod("handleOnMethodExit",
                String.class, String.class, String.class, Object.class);
            onMethodExitMethod.setAccessible(true);

        } catch (Exception e) {
            System.err.println("[SpyCallbackImpl] Failed to set actual handler: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 方法进入时的静态回调
     */
    public static Object[] onMethodEnter(String className, String methodName, String methodSignature, 
                                        Object target, Object[] arguments) {
        try {
            if (actualHandler != null && onMethodEnterMethod != null) {
                Object result = onMethodEnterMethod.invoke(actualHandler, className, methodName, methodSignature, target, arguments);
                if (result instanceof Object[]) {
                    return (Object[]) result;
                }
            }
        } catch (Exception e) {
            System.err.println("[SpyCallbackImpl] Error in onMethodEnter: " + e.getMessage());
            e.printStackTrace();
        }
        return new Object[0];
    }
    
    /**
     * 方法正常返回时的静态回调
     */
    public static void onMethodReturn(Object[] events, Object returnValue) {
        try {
            if (actualHandler != null && onMethodReturnMethod != null) {
                onMethodReturnMethod.invoke(actualHandler, events, returnValue);
            }
        } catch (Exception e) {
            System.err.println("[SpyCallbackImpl] Error in onMethodReturn: " + e.getMessage());
        }
    }
    
    /**
     * 方法抛出异常时的静态回调
     */
    public static void onMethodThrow(Object[] events, Throwable throwable) {
        try {
            if (actualHandler != null && onMethodThrowMethod != null) {
                onMethodThrowMethod.invoke(actualHandler, events, throwable);
            }
        } catch (Exception e) {
            System.err.println("[SpyCallbackImpl] Error in onMethodThrow: " + e.getMessage());
        }
    }
    
    /**
     * 方法退出时的静态回调
     */
    public static void onMethodExit(String className, String methodName, String methodSignature, Object returnValue) {
        try {
            if (actualHandler != null && onMethodExitMethod != null) {
                onMethodExitMethod.invoke(actualHandler, className, methodName, methodSignature, returnValue);
            }
        } catch (Exception e) {
            System.err.println("[SpyCallbackImpl] Error in onMethodExit: " + e.getMessage());
        }
    }
}
