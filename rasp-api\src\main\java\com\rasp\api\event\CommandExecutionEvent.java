package com.rasp.api.event;

/**
 * 命令执行事件
 */
public class CommandExecutionEvent extends HookEvent {
    
    private String command;
    private String[] commandArray;
    private String workingDirectory;
    private String[] environment;
    
    public String getCommand() {
        return command;
    }
    
    public void setCommand(String command) {
        this.command = command;
    }
    
    public String[] getCommandArray() {
        return commandArray;
    }
    
    public void setCommandArray(String[] commandArray) {
        this.commandArray = commandArray;
    }
    
    public String getWorkingDirectory() {
        return workingDirectory;
    }
    
    public void setWorkingDirectory(String workingDirectory) {
        this.workingDirectory = workingDirectory;
    }
    
    public String[] getEnvironment() {
        return environment;
    }
    
    public void setEnvironment(String[] environment) {
        this.environment = environment;
    }
    
    @Override
    public String toString() {
        return "CommandExecutionEvent{" +
                "command='" + command + '\'' +
                ", className='" + getClassName() + '\'' +
                ", methodName='" + getMethodName() + '\'' +
                '}';
    }
}
