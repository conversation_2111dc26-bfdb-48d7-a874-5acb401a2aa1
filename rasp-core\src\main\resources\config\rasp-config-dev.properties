# RASP Agent Configuration File - Development Environment
# This configuration is optimized for development and debugging

# ============================================================================
# Global Configuration
# ============================================================================

rasp.enabled=true
rasp.log.level=DEBUG
rasp.log.file=logs/rasp-agent-dev.log

# ============================================================================
# Rule Configuration - Development Mode
# ============================================================================

# HTTP Request Logging Rule - ENABLED for development monitoring
rasp.rule.HttpRequestLogRule.enabled=true

# Command Execution Security Rule - ENABLED for security testing
rasp.rule.CommandExecutionRule.enabled=true

# File Write Monitoring Rule - ENABLED for file operation monitoring
rasp.rule.FileWriteRule.enabled=true

# Method Call Logging Rule - ENABLED for detailed debugging (verbose!)
rasp.rule.MethodCallLogRule.enabled=true
