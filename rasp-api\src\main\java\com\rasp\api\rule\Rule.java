package com.rasp.api.rule;

import com.rasp.api.event.HookEvent;

/**
 * 规则接口
 * 所有规则实现都需要实现此接口
 */
public interface Rule {
    
    /**
     * 规则名称
     * @return 规则的唯一标识名称
     */
    String getName();
    
    /**
     * 规则描述
     * @return 规则的功能描述
     */
    String getDescription();
    
    /**
     * 规则版本
     * @return 规则的版本号
     */
    String getVersion();
    
    /**
     * 规则优先级
     * 数值越小优先级越高
     * @return 优先级数值
     */
    int getPriority();
    
    /**
     * 获取规则支持的事件类型
     * @return 支持的事件类型数组
     */
    HookEvent.EventType[] getSupportedEventTypes();
    
    /**
     * 判断规则是否支持指定的事件类型
     * @param eventType 事件类型
     * @return true表示支持，false表示不支持
     */
    boolean supportsEventType(HookEvent.EventType eventType);
    
    /**
     * 处理Hook事件
     * @param event Hook事件
     * @return 规则处理结果
     */
    RuleResult process(HookEvent event);
    
    /**
     * 规则是否启用
     * @return true表示启用，false表示禁用
     */
    boolean isEnabled();
    
    /**
     * 启用规则
     */
    void enable();
    
    /**
     * 禁用规则
     */
    void disable();
    
    /**
     * 规则初始化
     * 在规则加载时调用
     */
    void initialize();
    
    /**
     * 规则销毁
     * 在规则卸载时调用
     */
    void destroy();
}
