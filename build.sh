#!/bin/bash

# RASP Agent构建脚本

echo "Building RASP Agent..."

# 检查Maven是否安装
if ! command -v mvn &> /dev/null; then
    echo "Error: <PERSON><PERSON> is not installed or not in PATH"
    exit 1
fi

# 检查Java版本
java_version=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}')
echo "Java version: $java_version"

# 清理并编译
echo "Cleaning and compiling..."
mvn clean compile

if [ $? -ne 0 ]; then
    echo "Error: Compilation failed"
    exit 1
fi

# 打包
echo "Packaging..."
mvn package

if [ $? -ne 0 ]; then
    echo "Error: Packaging failed"
    exit 1
fi

# 检查生成的JAR文件
agent_jar="rasp-agent-bootstrap/target/rasp-agent-bootstrap-1.0.0.jar"
if [ -f "$agent_jar" ]; then
    echo "Build successful!"
    echo "Agent JAR: $agent_jar"
    
    # 显示JAR文件信息
    echo "JAR file size: $(du -h $agent_jar | cut -f1)"
    echo "JAR manifest:"
    jar -tf "$agent_jar" | grep -E "(MANIFEST.MF|\.class)" | head -10
    
    # 创建启动脚本
    cat > start-with-rasp.sh << 'EOF'
#!/bin/bash

# RASP Agent启动脚本
# 使用方法: ./start-with-rasp.sh <your-java-application>

RASP_AGENT_JAR="rasp-agent-bootstrap/target/rasp-agent-bootstrap-1.0.0.jar"

if [ ! -f "$RASP_AGENT_JAR" ]; then
    echo "Error: RASP Agent JAR not found: $RASP_AGENT_JAR"
    echo "Please run build.sh first"
    exit 1
fi

if [ $# -eq 0 ]; then
    echo "Usage: $0 <java-application-command>"
    echo "Example: $0 java -jar your-app.jar"
    exit 1
fi

echo "Starting application with RASP Agent..."
echo "Agent: $RASP_AGENT_JAR"
echo "Command: $@"

# 设置RASP相关的系统属性
export JAVA_OPTS="$JAVA_OPTS -javaagent:$RASP_AGENT_JAR"
export JAVA_OPTS="$JAVA_OPTS -Drasp.log.level=INFO"
export JAVA_OPTS="$JAVA_OPTS -Drasp.log.dir=./logs"

# 创建日志目录
mkdir -p logs

# 启动应用
exec "$@"
EOF

    chmod +x start-with-rasp.sh
    echo "Created startup script: start-with-rasp.sh"
    
else
    echo "Error: Agent JAR not found after build"
    exit 1
fi

echo "Build completed successfully!"
