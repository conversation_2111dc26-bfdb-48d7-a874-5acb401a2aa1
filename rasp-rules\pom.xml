<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.rasp</groupId>
        <artifactId>rasp-agent</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>rasp-rules</artifactId>
    <name>RASP Rules</name>
    <description>RASP Rule Implementations</description>

    <dependencies>
        <dependency>
            <groupId>com.rasp</groupId>
            <artifactId>rasp-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
