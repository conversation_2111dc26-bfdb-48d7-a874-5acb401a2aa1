package com.rasp.hooks.http;

import com.rasp.api.event.HookEvent;
import com.rasp.api.event.HttpRequestEvent;
import com.rasp.api.hook.AbstractHook;
import com.rasp.api.context.RequestContext;

// 移除直接的Servlet API依赖，使用反射访问
// import javax.servlet.http.HttpServletRequest;
// import javax.servlet.http.HttpSession;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * HTTP请求Hook
 * 用于采集HTTP请求的详细信息
 */
public class HttpRequestHook extends AbstractHook {
    
    @Override
    public String getName() {
        return "HttpRequestHook";
    }
    
    @Override
    public String getDescription() {
        return "Hook for capturing HTTP request information";
    }
    
    @Override
    public String[] getClassNamePatterns() {
        return new String[]{
            "javax.servlet.http.HttpServlet",
            "org.springframework.web.servlet.DispatcherServlet",
            "org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter",
            "org.springframework.web.method.support.InvocableHandlerMethod",
            "org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod"
        };
    }
    
    @Override
    public String[] getMethodNamePatterns() {
        return new String[]{
            "service",
            "doGet",
            "doPost",
            "doPut",
            "doDelete",
            "doFilter",
            "invokeForRequest",
            "invokeAndHandle"
        };
    }
    
    @Override
    public String[] getMethodSignaturePatterns() {
        return null; // 不限制方法签名
    }
    
    @Override
    public HookEvent onMethodEnter(String className, String methodName, String methodSignature,
                                  Object target, Object[] arguments) {
        
        Object request = extractHttpServletRequest(arguments);
        if (request == null) {
            return null;
        }

        HttpRequestEvent event = new HttpRequestEvent();
        event.setClassName(className);
        event.setMethodName(methodName);
        event.setMethodSignature(methodSignature);
        event.setArguments(arguments);

        // 采集HTTP请求信息 - 使用反射避免类加载问题
        try {
            Class<?> requestClass = request.getClass();

            // 基本请求信息
            Object requestURL = invokeMethodSafely(requestClass, request, "getRequestURL");
            if (requestURL != null) {
                event.setRequestUrl(requestURL.toString());
            }

            String method = (String) invokeMethodSafely(requestClass, request, "getMethod");
            event.setRequestMethod(method);

            String requestURI = (String) invokeMethodSafely(requestClass, request, "getRequestURI");
            event.setRequestPath(requestURI);

            String queryString = (String) invokeMethodSafely(requestClass, request, "getQueryString");
            event.setQueryString(queryString);

            String protocol = (String) invokeMethodSafely(requestClass, request, "getProtocol");
            event.setProtocol(protocol);

            String serverName = (String) invokeMethodSafely(requestClass, request, "getServerName");
            event.setServerName(serverName);

            Integer serverPort = (Integer) invokeMethodSafely(requestClass, request, "getServerPort");
            event.setServerPort(serverPort);

            // 客户端信息
            event.setClientIp(getClientIpAddressReflection(request));

            String userAgent = (String) invokeMethodSafely(requestClass, request, "getHeader", "User-Agent");
            event.setUserAgent(userAgent);

            String referer = (String) invokeMethodSafely(requestClass, request, "getHeader", "Referer");
            event.setReferer(referer);

            // Session信息
            Object session = invokeMethodSafely(requestClass, request, "getSession", false);
            if (session != null) {
                String sessionId = (String) invokeMethodSafely(session.getClass(), session, "getId");
                event.setSessionId(sessionId);
            }

            // 请求头
            Map<String, String> headers = extractHeadersReflection(request);
            event.setRequestHeaders(headers);

            // 请求参数
            Map<String, String[]> parameters = extractParametersReflection(request);
            event.setRequestParameters(parameters);
            
            // 请求体（对于POST等请求）
            String requestMethod = event.getRequestMethod();
            if ("POST".equalsIgnoreCase(requestMethod) ||
                "PUT".equalsIgnoreCase(requestMethod) ||
                "PATCH".equalsIgnoreCase(requestMethod)) {
                // 注意：读取请求体可能会影响后续处理，这里只是示例
                // 实际实现中可能需要更复杂的处理
                try {
                    String contentType = (String) invokeMethodSafely(requestClass, request, "getContentType");
                    if (contentType != null && contentType.contains("application/json")) {
                        // 可以尝试读取JSON请求体，但需要小心处理
                        event.setRequestBody("JSON body (not captured to avoid interference)");
                    } else if (contentType != null && contentType.contains("application/x-www-form-urlencoded")) {
                        // 表单数据已经在parameters中
                        event.setRequestBody("Form data captured in parameters");
                    }
                } catch (Exception e) {
                    logger.debug("Failed to get content type", e);
                }
            }
            
            logger.debug("HTTP request captured: {} {} from {}", 
                        event.getRequestMethod(), event.getRequestUrl(), event.getClientIp());
            
        } catch (Exception e) {
            logger.error("Error capturing HTTP request information", e);
        }

        // 将HTTP请求事件保存到线程上下文中，供其他Hook使用
        if (event != null) {
            RequestContext.setHttpRequestEvent(event);
            logger.debug("HTTP request event saved to context: {} {}", event.getRequestMethod(), event.getRequestUrl());
        }

        return event;
    }
    
    /**
     * 从方法参数中提取HttpServletRequest对象
     * @param arguments 方法参数
     * @return HttpServletRequest对象，如果没有找到返回null
     */
    private Object extractHttpServletRequest(Object[] arguments) {
        if (arguments == null) {
            return null;
        }

        for (Object arg : arguments) {
            if (arg != null) {
                String className = arg.getClass().getName();

                // 直接检查类名，避免类型转换问题
                if (className.equals("javax.servlet.http.HttpServletRequest") ||
                    className.contains("HttpServletRequest")) {
                    return arg;
                }

                // 对于Spring MVC的InvocableHandlerMethod，尝试从NativeWebRequest中获取
                if (className.contains("NativeWebRequest")) {
                    try {
                        // 使用反射获取HttpServletRequest，但不直接加载类
                        Object nativeRequest = invokeMethodSafely(arg.getClass(), arg, "getNativeRequest");
                        if (nativeRequest != null && nativeRequest.getClass().getName().contains("HttpServletRequest")) {
                            return nativeRequest;
                        }
                    } catch (Exception e) {
                        logger.debug("Failed to extract HttpServletRequest from NativeWebRequest", e);
                    }
                }

                // 对于Spring MVC的ServletWebRequest，直接获取request
                if (className.contains("ServletWebRequest")) {
                    try {
                        java.lang.reflect.Method getRequestMethod = arg.getClass().getMethod("getRequest");
                        Object request = getRequestMethod.invoke(arg);
                        if (request != null) {
                            return request;
                        }
                    } catch (Exception e) {
                        logger.debug("Failed to extract HttpServletRequest from ServletWebRequest", e);
                    }
                }
            }
        }

        return null;
    }

    /**
     * 安全的反射方法调用，处理访问控制问题
     */
    private Object invokeMethodSafely(Class<?> clazz, Object instance, String methodName, Object... args) {
        try {
            Class<?>[] paramTypes = new Class[args.length];
            for (int i = 0; i < args.length; i++) {
                if (args[i] == null) {
                    paramTypes[i] = Object.class; // 默认类型
                } else if (args[i] instanceof String) {
                    paramTypes[i] = String.class;
                } else if (args[i] instanceof Boolean) {
                    paramTypes[i] = boolean.class;
                } else if (args[i] instanceof Integer) {
                    paramTypes[i] = int.class;
                } else {
                    paramTypes[i] = args[i].getClass();
                }
            }

            java.lang.reflect.Method method = clazz.getMethod(methodName, paramTypes);
            method.setAccessible(true); // 设置可访问
            return method.invoke(instance, args);
        } catch (Exception e) {
            logger.debug("Failed to invoke method {} on class {}: {}", methodName, clazz.getName(), e.getMessage());
            return null;
        }
    }

    /**
     * 使用反射提取请求头信息
     */
    private Map<String, String> extractHeadersReflection(Object request) {
        Map<String, String> headers = new HashMap<>();
        try {
            Class<?> requestClass = request.getClass();
            Object headerNames = invokeMethodSafely(requestClass, request, "getHeaderNames");

            if (headerNames != null) {
                // 处理Enumeration
                Class<?> enumerationClass = headerNames.getClass();
                while (Boolean.TRUE.equals(invokeMethodSafely(enumerationClass, headerNames, "hasMoreElements"))) {
                    String headerName = (String) invokeMethodSafely(enumerationClass, headerNames, "nextElement");
                    if (headerName != null) {
                        String headerValue = (String) invokeMethodSafely(requestClass, request, "getHeader", headerName);
                        headers.put(headerName, headerValue);
                    }
                }
            }
        } catch (Exception e) {
            logger.debug("Failed to extract headers using reflection", e);
        }
        return headers;
    }

    /**
     * 使用反射提取请求参数
     */
    private Map<String, String[]> extractParametersReflection(Object request) {
        try {
            Class<?> requestClass = request.getClass();
            @SuppressWarnings("unchecked")
            Map<String, String[]> parameterMap = (Map<String, String[]>) invokeMethodSafely(requestClass, request, "getParameterMap");
            return parameterMap != null ? new HashMap<>(parameterMap) : new HashMap<>();
        } catch (Exception e) {
            logger.debug("Failed to extract parameters using reflection", e);
            return new HashMap<>();
        }
    }

    /**
     * 使用反射获取客户端IP地址
     */
    private String getClientIpAddressReflection(Object request) {
        try {
            Class<?> requestClass = request.getClass();

            // 检查X-Forwarded-For头
            String xForwardedFor = (String) invokeMethodSafely(requestClass, request, "getHeader", "X-Forwarded-For");
            if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
                return xForwardedFor.split(",")[0].trim();
            }

            // 检查X-Real-IP头
            String xRealIp = (String) invokeMethodSafely(requestClass, request, "getHeader", "X-Real-IP");
            if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
                return xRealIp;
            }

            // 使用getRemoteAddr
            String remoteAddr = (String) invokeMethodSafely(requestClass, request, "getRemoteAddr");
            return remoteAddr != null ? remoteAddr : "unknown";
        } catch (Exception e) {
            logger.debug("Failed to get client IP using reflection", e);
            return "unknown";
        }
    }
    
    // 旧的getClientIpAddress方法已被getClientIpAddressReflection替代
    
    @Override
    public void onMethodReturn(HookEvent event, Object returnValue) {
        super.onMethodReturn(event, returnValue);
        
        if (event instanceof HttpRequestEvent) {
            HttpRequestEvent httpEvent = (HttpRequestEvent) event;
            logger.debug("HTTP request completed: {} {} -> {}", 
                        httpEvent.getRequestMethod(), httpEvent.getRequestUrl(), returnValue);
        }
    }
    
    @Override
    public void onMethodThrow(HookEvent event, Throwable throwable) {
        super.onMethodThrow(event, throwable);
        
        if (event instanceof HttpRequestEvent) {
            HttpRequestEvent httpEvent = (HttpRequestEvent) event;
            logger.warn("HTTP request failed: {} {} -> {}", 
                       httpEvent.getRequestMethod(), httpEvent.getRequestUrl(), throwable.getMessage());
        }
    }
}
