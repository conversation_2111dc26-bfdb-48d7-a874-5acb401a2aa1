package com.rasp.api.event;

/**
 * 文件写入事件
 */
public class FileWriteEvent extends HookEvent {
    
    private String filePath;
    private String absolutePath;
    private boolean isAbsolutePath;
    private String fileExtension;
    private long fileSize;
    private String content;
    
    public String getFilePath() {
        return filePath;
    }
    
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
    
    public String getAbsolutePath() {
        return absolutePath;
    }
    
    public void setAbsolutePath(String absolutePath) {
        this.absolutePath = absolutePath;
    }
    
    public boolean isAbsolutePath() {
        return isAbsolutePath;
    }
    
    public void setIsAbsolutePath(boolean absolutePath) {
        isAbsolutePath = absolutePath;
    }
    
    public String getFileExtension() {
        return fileExtension;
    }
    
    public void setFileExtension(String fileExtension) {
        this.fileExtension = fileExtension;
    }
    
    public long getFileSize() {
        return fileSize;
    }
    
    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    @Override
    public String toString() {
        return "FileWriteEvent{" +
                "filePath='" + filePath + '\'' +
                ", absolutePath='" + absolutePath + '\'' +
                ", isAbsolutePath=" + isAbsolutePath +
                ", className='" + getClassName() + '\'' +
                ", methodName='" + getMethodName() + '\'' +
                '}';
    }
} 