package com.rasp.hooks.file;

import com.rasp.api.event.HookEvent;
import com.rasp.api.event.FileWriteEvent;
import com.rasp.api.hook.Hook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.lang.reflect.Field;

/**
 * 增强版文件写入Hook
 * 覆盖靶场中所有文件写入操作，直接Hook目标方法
 */
public class FileWriteHook implements Hook {
    
    private static final Logger logger = LoggerFactory.getLogger(FileWriteHook.class);
    
    private boolean enabled = true;
    
    @Override
    public String getName() {
        return "FileWriteHook";
    }
    
    @Override
    public String getDescription() {
        return "Hook for capturing all file write operations including reflection-based calls";
    }
    
    @Override
    public String getVersion() {
        return "1.0.0";
    }
    
    @Override
    public String[] getClassNamePatterns() {
        return new String[]{
            // 传统IO类 - 靶场直接使用
            "java.io.FileOutputStream",      // fileOutStreamWriteFile使用
            "java.io.RandomAccessFile",      // randomAccessFileWriteFile使用
            "java.io.FileWriter",
            "java.io.PrintWriter",
            "java.io.BufferedWriter",
            "java.io.PrintStream",
            
            // NIO类 - 靶场通过反射使用，但我们直接Hook目标方法
            "java.nio.file.Files",           // filesWrite通过反射调用Files.write()
            "java.nio.channels.FileChannel",
            
            // Apache Commons IO - 靶场FileUploadController使用
            "org.apache.commons.io.FileUtils",
            "org.apache.commons.io.IOUtils",
            
            // Spring Framework - 常见文件操作
            "org.springframework.util.FileCopyUtils",
            "org.springframework.web.multipart.commons.CommonsMultipartFile",
            
            // 其他常见文件写入库
            "java.util.zip.ZipOutputStream",
            "java.util.jar.JarOutputStream"
        };
    }
    
    @Override
    public String[] getMethodNamePatterns() {
        return new String[]{
            "<init>",           // 构造方法
            "write",           // 基本写入方法 - 靶场三个方法都会最终调用write
            "append",          // 追加写入
            "print",           // 打印输出
            "println",         // 打印换行
            "printf",          // 格式化打印
            "flush",           // 刷新缓冲区
            "transferTo",      // 文件传输
            
            // NIO相关方法
            "newOutputStream",
            "newBufferedWriter",
            
            // Apache Commons方法 - 靶场FileUploadController使用
            "copyInputStreamToFile",  // FileUploadController使用
            "writeStringToFile",
            "writeByteArrayToFile",
            "copyFile",
            
            // Spring方法
            "copy"
        };
    }
    
    @Override
    public String[] getMethodSignaturePatterns() {
        return null; // 不限制方法签名
    }
    
    @Override
    public boolean shouldHookClass(String className) {
        String[] patterns = getClassNamePatterns();
        for (String pattern : patterns) {
            if (className.equals(pattern)) {
                return true;
            }
        }
        return false;
    }
    
    @Override
    public boolean shouldHookMethod(String className, String methodName, String methodSignature) {
        boolean classMatch = shouldHookClass(className);
        boolean methodMatch = false;
        
        if (classMatch) {
            String[] patterns = getMethodNamePatterns();
            for (String pattern : patterns) {
                if (methodName.equals(pattern)) {
                    methodMatch = true;
                    break;
                }
            }
        }
        
        boolean shouldHook = classMatch && methodMatch;
        
        if (shouldHook) {
            logger.debug("[FileWriteHook] Will hook method: {}.{}({})", className, methodName, methodSignature);
        }
        
        return shouldHook;
    }
    
    @Override
    public HookEvent onMethodEnter(String className, String methodName, String methodSignature,
                                   Object target, Object[] arguments) {
        FileWriteEvent event = new FileWriteEvent();
        event.setEventType(HookEvent.EventType.FILE_WRITE);
        event.setClassName(className);
        event.setMethodName(methodName);
        event.setMethodSignature(methodSignature);
        event.setArguments(arguments);
        
        try {
            String filePath = extractFilePath(className, methodName, target, arguments);
            
            if (filePath != null) {
                logger.info("[FileWriteHook] Captured file operation: {}.{} -> {}", className, methodName, filePath);
                
                event.setFilePath(filePath);
                enrichFileEvent(event, filePath);
            }
            
        } catch (Exception e) {
            logger.error("[FileWriteHook] Error in file write capture for {}.{}: {}", 
                        className, methodName, e.getMessage());
        }
        
        return event;
    }
    
    @Override
    public void onMethodReturn(HookEvent event, Object returnValue) {
        if (event instanceof FileWriteEvent) {
            FileWriteEvent fileEvent = (FileWriteEvent) event;
            logger.debug("[FileWriteHook] Method return: {}.{} -> {} (file: {})", 
                        event.getClassName(), event.getMethodName(), returnValue, fileEvent.getFilePath());
        }
    }
    
    @Override
    public void onMethodThrow(HookEvent event, Throwable throwable) {
        if (event instanceof FileWriteEvent) {
            FileWriteEvent fileEvent = (FileWriteEvent) event;
            logger.warn("[FileWriteHook] Method throw: {}.{} -> {} (file: {})", 
                       event.getClassName(), event.getMethodName(), throwable.getMessage(), fileEvent.getFilePath());
        }
    }
    
    @Override
    public boolean isEnabled() {
        return enabled;
    }
    
    @Override
    public void enable() {
        this.enabled = true;
        logger.info("[FileWriteHook] Hook enabled");
    }
    
    @Override
    public void disable() {
        this.enabled = false;
        logger.info("[FileWriteHook] Hook disabled");
    }
    
    /**
     * 简化的文件路径提取方法
     * 直接处理靶场中的文件写入场景，不再处理复杂的反射调用
     */
    private String extractFilePath(String className, String methodName, Object target, Object[] arguments) {
        try {
            // 1. FileOutputStream处理 - 靶场fileOutStreamWriteFile使用
            if ("java.io.FileOutputStream".equals(className)) {
                return handleFileOutputStream(methodName, target, arguments);
            }
            
            // 2. RandomAccessFile处理 - 靶场randomAccessFileWriteFile使用
            else if ("java.io.RandomAccessFile".equals(className)) {
                return handleRandomAccessFile(methodName, target, arguments);
            }
            
            // 3. NIO Files处理 - 靶场filesWrite通过反射调用，但我们直接Hook这里
            else if ("java.nio.file.Files".equals(className)) {
                return handleNIOFiles(methodName, arguments);
            }
            
            // 4. FileWriter处理
            else if ("java.io.FileWriter".equals(className)) {
                return handleFileWriter(methodName, target, arguments);
            }
            
            // 5. PrintWriter处理
            else if ("java.io.PrintWriter".equals(className)) {
                return handlePrintWriter(methodName, target, arguments);
            }
            
            // 6. Apache Commons FileUtils处理 - 靶场FileUploadController使用
            else if ("org.apache.commons.io.FileUtils".equals(className)) {
                return handleApacheFileUtils(methodName, arguments);
            }
            
            // 7. 其他常见文件写入类
            else if (className.contains("OutputStream") || className.contains("Writer")) {
                return handleGenericFileWriter(methodName, target, arguments);
            }
            
        } catch (Exception e) {
            logger.debug("Failed to extract file path from {}.{}: {}", className, methodName, e.getMessage());
        }
        
        return null;
    }
    

    
    /**
     * 处理FileOutputStream - 靶场fileOutStreamWriteFile使用
     */
    private String handleFileOutputStream(String methodName, Object target, Object[] arguments) {
        if ("<init>".equals(methodName)) {
            // 构造方法
            if (arguments != null && arguments.length > 0) {
                Object fileArg = arguments[0];
                if (fileArg instanceof String) {
                    String filePath = (String) fileArg;
                    logger.info("[FileWriteHook] Captured FileOutputStream.<init>, file path: {}", filePath);
                    return filePath;
                } else if (fileArg instanceof File) {
                    String filePath = ((File) fileArg).getPath();
                    logger.info("[FileWriteHook] Captured FileOutputStream.<init>, file path: {}", filePath);
                    return filePath;
                }
            }
        } else if ("write".equals(methodName) && target != null) {
            // 写入方法，通过反射获取路径
            String filePath = extractPathFromFileStream(target);
            if (filePath != null) {
                logger.info("[FileWriteHook] Captured FileOutputStream.write, file path: {}", filePath);
            }
            return filePath;
        }
        return null;
    }
    
    /**
     * 处理RandomAccessFile - 靶场randomAccessFileWriteFile使用
     */
    private String handleRandomAccessFile(String methodName, Object target, Object[] arguments) {
        if ("<init>".equals(methodName)) {
            // RandomAccessFile构造方法
            if (arguments != null && arguments.length > 0) {
                Object fileArg = arguments[0];
                if (fileArg instanceof String) {
                    String filePath = (String) fileArg;
                    logger.info("[FileWriteHook] Captured RandomAccessFile.<init>, file path: {}", filePath);
                    return filePath;
                } else if (fileArg instanceof File) {
                    String filePath = ((File) fileArg).getPath();
                    logger.info("[FileWriteHook] Captured RandomAccessFile.<init>, file path: {}", filePath);
                    return filePath;
                }
            }
        } else if ("write".equals(methodName) && target != null) {
            // 写入方法，通过反射获取路径
            String filePath = extractPathFromRandomAccessFile(target);
            if (filePath != null) {
                logger.info("[FileWriteHook] Captured RandomAccessFile.write, file path: {}", filePath);
            }
            return filePath;
        }
        return null;
    }
    
    /**
     * 处理Apache Commons FileUtils - 新增：靶场FileUploadController使用
     */
    private String handleApacheFileUtils(String methodName, Object[] arguments) {
        if ("copyInputStreamToFile".equals(methodName)) {
            // copyInputStreamToFile(InputStream, File)
            if (arguments != null && arguments.length >= 2) {
                Object fileArg = arguments[1];  // 第二个参数是目标文件
                if (fileArg instanceof File) {
                    return ((File) fileArg).getPath();
                } else if (fileArg instanceof String) {
                    return (String) fileArg;
                }
            }
        } else if ("writeStringToFile".equals(methodName) || "writeByteArrayToFile".equals(methodName)) {
            // writeStringToFile(File, String) 或 writeByteArrayToFile(File, byte[])
            if (arguments != null && arguments.length > 0) {
                Object fileArg = arguments[0];  // 第一个参数是文件
                if (fileArg instanceof File) {
                    return ((File) fileArg).getPath();
                } else if (fileArg instanceof String) {
                    return (String) fileArg;
                }
            }
        }
        return null;
    }
    
    /**
     * 处理NIO Files - 直接Hook Files.write()方法
     * 这能捕获靶场filesWrite方法的反射调用
     */
    private String handleNIOFiles(String methodName, Object[] arguments) {
        if ("write".equals(methodName) && arguments != null && arguments.length > 0) {
            Object pathArg = arguments[0];
            if (pathArg != null) {
                String filePath = pathArg.toString();
                logger.info("[FileWriteHook] Captured Files.write() call, file path: {}", filePath);
                return filePath;
            }
        }
        return null;
    }
    
    /**
     * 处理FileWriter
     */
    private String handleFileWriter(String methodName, Object target, Object[] arguments) {
        if ("<init>".equals(methodName)) {
            if (arguments != null && arguments.length > 0) {
                Object fileArg = arguments[0];
                if (fileArg instanceof String) {
                    return (String) fileArg;
                } else if (fileArg instanceof File) {
                    return ((File) fileArg).getPath();
                }
            }
        }
        return null;
    }
    
    /**
     * 处理PrintWriter
     */
    private String handlePrintWriter(String methodName, Object target, Object[] arguments) {
        if ("<init>".equals(methodName)) {
            if (arguments != null && arguments.length > 0) {
                Object fileArg = arguments[0];
                if (fileArg instanceof String) {
                    return (String) fileArg;
                } else if (fileArg instanceof File) {
                    return ((File) fileArg).getPath();
                }
            }
        }
        return null;
    }
    
    /**
     * 处理通用文件写入类
     */
    private String handleGenericFileWriter(String methodName, Object target, Object[] arguments) {
        // 尝试从目标对象中提取文件路径
        if (target != null) {
            return extractPathFromFileStream(target);
        }
        return null;
    }
    
    /**
     * 从FileOutputStream中提取文件路径
     */
    private String extractPathFromFileStream(Object target) {
        try {
            // 尝试获取path字段
            Field pathField = target.getClass().getDeclaredField("path");
            pathField.setAccessible(true);
            Object pathObj = pathField.get(target);
            if (pathObj instanceof String) {
                return (String) pathObj;
            }
        } catch (Exception e) {
            // 如果path字段不存在，尝试其他方式
            try {
                // 尝试获取fd字段然后获取路径
                Field fdField = target.getClass().getDeclaredField("fd");
                fdField.setAccessible(true);
                Object fd = fdField.get(target);
                if (fd != null) {
                    // 某些JDK版本的FileDescriptor有path信息
                    return "fd-based-path";
                }
            } catch (Exception ex) {
                logger.debug("Could not extract path from FileOutputStream: {}", ex.getMessage());
            }
        }
        return "unknown-filestream-path";
    }
    
    /**
     * 从RandomAccessFile中提取文件路径
     */
    private String extractPathFromRandomAccessFile(Object target) {
        try {
            // RandomAccessFile通常有path字段
            Field pathField = target.getClass().getDeclaredField("path");
            pathField.setAccessible(true);
            Object pathObj = pathField.get(target);
            if (pathObj instanceof String) {
                return (String) pathObj;
            }
        } catch (Exception e) {
            logger.debug("Could not extract path from RandomAccessFile: {}", e.getMessage());
        }
        return "unknown-randomaccessfile-path";
    }
    
    /**
     * 丰富文件事件信息
     */
    private void enrichFileEvent(FileWriteEvent event, String filePath) {
        try {
            File file = new File(filePath);
            String absolutePath = file.getAbsolutePath();
            event.setAbsolutePath(absolutePath);
            
            // 判断是否为绝对路径
            boolean isAbsolute = file.isAbsolute();
            event.setIsAbsolutePath(isAbsolute);
            
            // 提取文件扩展名
            String fileName = file.getName();
            int lastDot = fileName.lastIndexOf('.');
            if (lastDot > 0 && lastDot < fileName.length() - 1) {
                String extension = fileName.substring(lastDot + 1);
                event.setFileExtension(extension);
            }
            
            // 设置文件大小（如果文件已存在）
            if (file.exists()) {
                event.setFileSize(file.length());
            }
            
        } catch (Exception e) {
            logger.debug("Error enriching file event: {}", e.getMessage());
        }
    }
} 