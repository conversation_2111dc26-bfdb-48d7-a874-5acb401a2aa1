package com.rasp.api.event;

import java.util.Map;

/**
 * HTTP请求事件
 * 继承自HookEvent，包含HTTP请求特有的信息
 */
public class HttpRequestEvent extends HookEvent {
    
    /**
     * 请求URL
     */
    private String requestUrl;
    
    /**
     * 请求方法 (GET, POST, PUT, DELETE等)
     */
    private String requestMethod;
    
    /**
     * 请求头
     */
    private Map<String, String> requestHeaders;
    
    /**
     * 请求参数
     */
    private Map<String, String[]> requestParameters;
    
    /**
     * 请求体
     */
    private String requestBody;
    
    /**
     * 客户端IP
     */
    private String clientIp;
    
    /**
     * User-Agent
     */
    private String userAgent;
    
    /**
     * Referer
     */
    private String referer;
    
    /**
     * Session ID
     */
    private String sessionId;
    
    /**
     * 请求协议
     */
    private String protocol;
    
    /**
     * 服务器名称
     */
    private String serverName;
    
    /**
     * 服务器端口
     */
    private int serverPort;
    
    /**
     * 请求路径
     */
    private String requestPath;
    
    /**
     * 查询字符串
     */
    private String queryString;
    
    public HttpRequestEvent() {
        super();
        setEventType(EventType.HTTP_REQUEST);
    }
    
    // Getters and Setters
    public String getRequestUrl() {
        return requestUrl;
    }
    
    public void setRequestUrl(String requestUrl) {
        this.requestUrl = requestUrl;
    }
    
    public String getRequestMethod() {
        return requestMethod;
    }
    
    public void setRequestMethod(String requestMethod) {
        this.requestMethod = requestMethod;
    }
    
    public Map<String, String> getRequestHeaders() {
        return requestHeaders;
    }
    
    public void setRequestHeaders(Map<String, String> requestHeaders) {
        this.requestHeaders = requestHeaders;
    }
    
    public Map<String, String[]> getRequestParameters() {
        return requestParameters;
    }
    
    public void setRequestParameters(Map<String, String[]> requestParameters) {
        this.requestParameters = requestParameters;
    }
    
    public String getRequestBody() {
        return requestBody;
    }
    
    public void setRequestBody(String requestBody) {
        this.requestBody = requestBody;
    }
    
    public String getClientIp() {
        return clientIp;
    }
    
    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }
    
    public String getUserAgent() {
        return userAgent;
    }
    
    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }
    
    public String getReferer() {
        return referer;
    }
    
    public void setReferer(String referer) {
        this.referer = referer;
    }
    
    public String getSessionId() {
        return sessionId;
    }
    
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
    
    public String getProtocol() {
        return protocol;
    }
    
    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }
    
    public String getServerName() {
        return serverName;
    }
    
    public void setServerName(String serverName) {
        this.serverName = serverName;
    }
    
    public int getServerPort() {
        return serverPort;
    }
    
    public void setServerPort(int serverPort) {
        this.serverPort = serverPort;
    }
    
    public String getRequestPath() {
        return requestPath;
    }
    
    public void setRequestPath(String requestPath) {
        this.requestPath = requestPath;
    }
    
    public String getQueryString() {
        return queryString;
    }
    
    public void setQueryString(String queryString) {
        this.queryString = queryString;
    }
    
    @Override
    public String toString() {
        return "HttpRequestEvent{" +
                "requestUrl='" + requestUrl + '\'' +
                ", requestMethod='" + requestMethod + '\'' +
                ", clientIp='" + clientIp + '\'' +
                ", userAgent='" + userAgent + '\'' +
                ", requestPath='" + requestPath + '\'' +
                ", timestamp=" + getTimestamp() +
                ", threadName='" + getThreadName() + '\'' +
                '}';
    }
}
