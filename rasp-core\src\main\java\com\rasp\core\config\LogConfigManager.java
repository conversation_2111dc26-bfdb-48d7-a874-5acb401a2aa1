package com.rasp.core.config;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import org.slf4j.LoggerFactory;

/**
 * 日志配置管理器
 * 负责将RASP配置文件中的日志设置应用到Logback配置
 */
public class LogConfigManager {
    
    private static final org.slf4j.Logger logger = LoggerFactory.getLogger(LogConfigManager.class);
    
    /**
     * 应用RASP配置到日志系统
     * @param configManager RASP配置管理器
     */
    public static void applyLogConfiguration(RaspConfigManager configManager) {
        try {
            // 获取日志级别配置
            String logLevel = configManager.getProperty("rasp.log.level", "INFO");
            String logFile = configManager.getProperty("rasp.log.file", "logs/rasp-agent.log");
            
            // 设置系统属性，让Logback配置文件能够读取
            System.setProperty("rasp.log.level", logLevel);
            System.setProperty("rasp.log.file", logFile);
            
            // 动态设置日志级别
            setLogLevel(logLevel);
            
            logger.info("Log configuration applied - Level: {}, File: {}", logLevel, logFile);
            
        } catch (Exception e) {
            logger.error("Failed to apply log configuration", e);
        }
    }
    
    /**
     * 设置RASP相关包的日志级别
     * @param levelStr 日志级别字符串
     */
    private static void setLogLevel(String levelStr) {
        try {
            Level level = Level.valueOf(levelStr.toUpperCase());
            LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
            
            // 设置RASP相关包的日志级别
            String[] raspPackages = {
                "com.rasp.core",
                "com.rasp.hooks", 
                "com.rasp.rules",
                "com.rasp.api"
            };
            
            for (String packageName : raspPackages) {
                Logger packageLogger = loggerContext.getLogger(packageName);
                packageLogger.setLevel(level);
                logger.debug("Set log level for package {} to {}", packageName, level);
            }
            
            // 设置根日志级别
            Logger rootLogger = loggerContext.getLogger(Logger.ROOT_LOGGER_NAME);
            rootLogger.setLevel(level);
            
            logger.info("Log level set to: {}", level);
            
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid log level: {}, using INFO", levelStr);
            setLogLevel("INFO");
        }
    }
    
    /**
     * 设置特定类的日志级别
     * @param className 类名
     * @param levelStr 日志级别
     */
    public static void setClassLogLevel(String className, String levelStr) {
        try {
            Level level = Level.valueOf(levelStr.toUpperCase());
            LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
            Logger classLogger = loggerContext.getLogger(className);
            classLogger.setLevel(level);
            
            logger.info("Set log level for class {} to {}", className, level);
            
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid log level for class {}: {}", className, levelStr);
        }
    }
    
    /**
     * 重新加载日志配置
     * @param configManager RASP配置管理器
     */
    public static void reloadLogConfiguration(RaspConfigManager configManager) {
        logger.info("Reloading log configuration...");
        applyLogConfiguration(configManager);
    }
    
    /**
     * 获取当前日志级别
     * @param loggerName 日志器名称
     * @return 日志级别
     */
    public static String getCurrentLogLevel(String loggerName) {
        LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
        Logger targetLogger = loggerContext.getLogger(loggerName);
        Level level = targetLogger.getLevel();
        return level != null ? level.toString() : "INHERITED";
    }
    
    /**
     * 打印当前日志配置信息
     */
    public static void printLogConfiguration() {
        System.out.println("=== Current Log Configuration ===");
        
        String[] importantLoggers = {
            Logger.ROOT_LOGGER_NAME,
            "com.rasp.core",
            "com.rasp.hooks",
            "com.rasp.rules",
            "com.rasp.core.rule.RuleEngine",
            "com.rasp.core.hook.HookManager"
        };
        
        for (String loggerName : importantLoggers) {
            String level = getCurrentLogLevel(loggerName);
            System.out.println(String.format("%-30s: %s", loggerName, level));
        }
        
        System.out.println("=== End Log Configuration ===");
    }
    
    /**
     * 启用调试模式
     */
    public static void enableDebugMode() {
        logger.info("Enabling debug mode for RASP components...");
        setLogLevel("DEBUG");
    }
    
    /**
     * 启用生产模式（WARN级别）
     */
    public static void enableProductionMode() {
        logger.info("Enabling production mode for RASP components...");
        setLogLevel("WARN");
    }
    
    /**
     * 临时设置日志级别（不修改配置文件）
     * @param levelStr 日志级别
     */
    public static void setTemporaryLogLevel(String levelStr) {
        logger.info("Setting temporary log level to: {}", levelStr);
        setLogLevel(levelStr);
    }
}
