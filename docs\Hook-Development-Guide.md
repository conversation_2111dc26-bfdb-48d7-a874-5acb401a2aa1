# RASP Hook开发指南

## 概述

本文档基于文件写入Hook(`FileWriteHook`)的开发经验，总结了RASP Hook开发的完整思路、设计模式和最佳实践，为后续开发其他安全Hook提供参考。

## 架构设计原则

### 1. 分层职责划分

RASP Hook系统采用清晰的分层架构：

```
┌─────────────────┐
│  Hook Layer     │ ← 负责拦截和数据捕获
├─────────────────┤
│  Event Layer    │ ← 负责事件封装和传递
├─────────────────┤
│  Rule Layer     │ ← 负责安全规则判断
├─────────────────┤
│  Action Layer   │ ← 负责执行安全动作
└─────────────────┘
```

**各层职责**：
- **Hook Layer**: 拦截目标方法调用，提取关键信息
- **Event Layer**: 将原始数据封装成标准化事件对象
- **Rule Layer**: 基于事件数据进行安全分析和威胁判断
- **Action Layer**: 执行日志记录、告警、阻断等安全动作

### 2. 组件设计模式

每个Hook功能需要实现4个核心组件：

```
FileWriteHook (Hook实现)
    ↓ 产生
FileWriteEvent (事件对象)
    ↓ 传递给
FileWriteRule (安全规则)
    ↓ 生成
RuleResult (处理结果)
```

## 开发步骤详解

### 第一步：设计事件对象

**目的**: 定义Hook需要捕获和传递的数据结构

**实现要点**：
```java
public class FileWriteEvent extends HookEvent {
    // 核心业务数据
    private String filePath;           // 文件路径
    private String absolutePath;       // 绝对路径
    private boolean isAbsolutePath;    // 是否绝对路径
    private String fileExtension;      // 文件扩展名
    
    // 继承的通用数据
    // - className: 被Hook的类名
    // - methodName: 被Hook的方法名
    // - arguments: 方法参数
    // - timestamp: 时间戳
}
```

**设计原则**：
1. **业务相关性**: 只包含与安全判断相关的字段
2. **预处理优先**: 在Hook层就完成数据解析和格式化
3. **易于扩展**: 预留扩展字段，便于后续功能增强

### 第二步：实现Hook拦截逻辑

**核心职责**: 方法拦截、参数解析、数据提取

```java
public class FileWriteHook extends AbstractHook {
    
    // 1. 定义拦截目标
    @Override
    public String[] getClassNamePatterns() {
        return new String[]{
            "java.io.FileOutputStream",
            "java.io.FileWriter",
            "java.nio.file.Files"
        };
    }
    
    @Override
    public String[] getMethodNamePatterns() {
        return new String[]{"<init>", "write"};
    }
    
    // 2. 实现拦截逻辑
    @Override
    public HookEvent onMethodEnter(String className, String methodName, 
                                  String methodSignature, Object target, Object[] arguments) {
        // 创建事件对象
        FileWriteEvent event = new FileWriteEvent();
        
        // 提取和解析数据
        String filePath = extractFilePath(className, methodName, target, arguments);
        
        // 数据预处理
        if (filePath != null) {
            File file = new File(filePath);
            event.setFilePath(filePath);
            event.setAbsolutePath(file.getAbsolutePath());
            event.setIsAbsolutePath(file.isAbsolute());
            event.setFileExtension(extractExtension(file.getName()));
        }
        
        return event;
    }
}
```

**关键技术点**：

1. **多类型支持**: 同时支持FileOutputStream、FileWriter等多种实现
2. **方法兼容**: 同时Hook构造方法和写入方法，提高覆盖率
3. **反射技术**: 通过反射提取运行时无法直接获取的信息
4. **错误处理**: 优雅处理反射失败等异常情况

### 第三步：编写安全规则

**核心职责**: 威胁检测、风险评估、动作决策

```java
public class FileWriteRule extends AbstractRule {
    
    // 定义威胁检测逻辑
    @Override
    public RuleResult evaluate(HookEvent event) {
        FileWriteEvent fileEvent = (FileWriteEvent) event;
        String filePath = fileEvent.getFilePath();
        
        // 1. 基础威胁检测
        if (fileEvent.isAbsolutePath()) {
            return createBlockResult("Absolute path write detected: " + filePath);
        }
        
        // 2. 文件类型检测
        if (isDangerousFileType(fileEvent.getFileExtension())) {
            return createBlockResult("Dangerous file type: " + fileEvent.getFileExtension());
        }
        
        // 3. 上下文关联检测
        if (isPathFromUserInput(filePath)) {
            return createBlockResult("File path from user input: " + filePath);
        }
        
        // 4. 正常情况记录
        return createLogResult("File write monitored: " + filePath);
    }
}
```

**规则设计模式**：

1. **渐进式检测**: 从简单到复杂，逐步增加检测深度
2. **上下文关联**: 结合HTTP请求上下文进行综合判断
3. **可配置性**: 支持规则参数配置和动态调整
4. **误报控制**: 平衡安全性和可用性

### 第四步：集成到核心系统

**注册Hook**:
```java
// 在RaspCore中注册
hookManager.registerHook(new FileWriteHook());
```

**注册规则**:
```java
// 在RuleEngine中注册
ruleEngine.registerRule(new FileWriteRule());
```

## 技术难点与解决方案

### 1. 递归Hook问题

**问题**: Hook可能触发自身日志输出，导致无限递归

**解决方案**:
```java
// HookManager中的防护机制
private static final ThreadLocal<Boolean> isProcessing = ThreadLocal.withInitial(() -> false);

// RASP内部包名过滤
private static final String[] RASP_INTERNAL_PACKAGES = {
    "com.rasp.", "ch.qos.logback.", "org.slf4j."
};

public List<HookEvent> onMethodEnter(...) {
    if (isProcessing.get() || isRaspInternalClass(className)) {
        return new ArrayList<>();
    }
    
    try {
        isProcessing.set(true);
        // 执行Hook逻辑
    } finally {
        isProcessing.set(false);
    }
}
```

### 2. 通配符匹配问题

**问题**: 类名模式匹配不准确，可能匹配不到代理类、内部类

**解决方案**:
```java
// AbstractHook中的双重匹配机制
@Override
public boolean shouldHookClass(String className) {
    // 1. 先尝试正则表达式匹配
    for (Pattern pattern : classNamePatterns) {
        if (pattern.matcher(className).matches()) {
            return true;
        }
    }
    
    // 2. 失败后使用简单匹配作为fallback
    for (String pattern : rawPatterns) {
        if (!pattern.contains("*") && !pattern.contains("?")) {
            if (className.equals(pattern) || className.contains(pattern)) {
                return true;
            }
        }
    }
    
    return false;
}
```

### 3. 文件路径提取困难

**问题**: write方法调用时无法直接获取文件路径

**解决方案**:
```java
// 多策略路径提取
private String extractFilePath(String className, String methodName, Object target, Object[] arguments) {
    if ("<init>".equals(methodName)) {
        // 构造方法：直接从参数获取
        return extractFromConstructorArgs(arguments);
    } else if ("write".equals(methodName)) {
        // 写入方法：通过反射获取
        return extractFromReflection(target);
    }
    return null;
}

private String extractFromReflection(Object target) {
    try {
        Field pathField = target.getClass().getDeclaredField("path");
        pathField.setAccessible(true);
        return (String) pathField.get(target);
    } catch (Exception e) {
        return "unknown-file-path";  // 优雅降级
    }
}
```

## 最佳实践总结

### 1. Hook设计原则

- **单一职责**: 每个Hook只关注一个特定的安全领域
- **最小侵入**: 尽量减少对应用程序性能的影响
- **错误隔离**: Hook异常不应影响业务逻辑执行
- **可观测性**: 提供充分的日志和调试信息

### 2. 事件对象设计

- **标准化**: 统一的事件基类和字段命名规范
- **完整性**: 包含规则判断所需的所有信息
- **轻量化**: 避免包含过多冗余数据
- **序列化友好**: 支持网络传输和持久化

### 3. 规则编写规范

- **分层检测**: 从基础到高级，逐步细化检测逻辑
- **可配置**: 支持阈值、黑白名单等配置参数
- **上下文感知**: 充分利用HTTP请求等上下文信息
- **误报优化**: 持续优化规则，减少误报率

### 4. 性能优化

- **延迟初始化**: 规则和模式编译延迟到首次使用
- **缓存策略**: 缓存频繁使用的计算结果
- **异步处理**: 将重型操作异步化，避免阻塞业务
- **批量处理**: 对于高频事件考虑批量处理

## Hook开发检查清单

### 开发阶段
- [ ] 确定Hook目标类和方法
- [ ] 设计事件对象结构
- [ ] 实现Hook拦截逻辑
- [ ] 处理特殊情况（内部类、代理类等）
- [ ] 编写安全检测规则
- [ ] 添加必要的配置选项

### 测试阶段
- [ ] 单元测试覆盖
- [ ] 集成测试验证
- [ ] 性能基准测试
- [ ] 误报率评估
- [ ] 递归Hook测试
- [ ] 异常场景测试

### 文档阶段
- [ ] 编写Hook使用说明
- [ ] 记录配置参数
- [ ] 提供示例场景
- [ ] 说明限制和注意事项

## 扩展方向

基于这套框架，可以快速开发其他安全Hook：

1. **SQL注入检测Hook**: Hook JDBC相关类
2. **反序列化漏洞Hook**: Hook ObjectInputStream
3. **SSRF检测Hook**: Hook URL连接相关类
4. **XXE检测Hook**: Hook XML解析器
5. **代码执行Hook**: Hook Runtime.exec等方法

每个新Hook都可以遵循相同的四层架构和开发模式，实现快速开发和部署。

---

*本文档基于FileWriteHook v1.0的开发经验编写，随着系统演进会持续更新完善。* 