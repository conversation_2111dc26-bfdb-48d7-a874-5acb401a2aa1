package com.rasp.core.rule;

import com.rasp.api.event.HookEvent;
import com.rasp.api.rule.Rule;
import com.rasp.api.rule.RuleResult;
import com.rasp.core.classloader.ModuleClassLoader;
import com.rasp.core.config.RaspConfigManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 规则引擎
 * 负责规则的加载、卸载、管理和执行
 */
public class RuleEngine {
    
    private static final Logger logger = LoggerFactory.getLogger(RuleEngine.class);
    
    /**
     * 单例实例
     */
    private static volatile RuleEngine instance;
    
    /**
     * 已注册的规则列表，按优先级排序
     */
    private final List<Rule> rules = new CopyOnWriteArrayList<>();
    
    /**
     * 规则名称到规则实例的映射
     */
    private final Map<String, Rule> ruleMap = new ConcurrentHashMap<>();
    
    /**
     * 规则类加载器映射
     */
    private final Map<String, ModuleClassLoader> ruleClassLoaders = new ConcurrentHashMap<>();
    
    /**
     * 事件类型到规则列表的映射，用于快速查找
     */
    private final Map<HookEvent.EventType, List<Rule>> eventTypeRuleMap = new ConcurrentHashMap<>();
    
    private RuleEngine() {
    }
    
    /**
     * 获取单例实例
     * @return RuleEngine实例
     */
    public static RuleEngine getInstance() {
        if (instance == null) {
            synchronized (RuleEngine.class) {
                if (instance == null) {
                    instance = new RuleEngine();
                }
            }
        }
        return instance;
    }
    
    /**
     * 注册规则
     * @param rule 规则实例
     */
    public void registerRule(Rule rule) {
        if (rule == null) {
            throw new IllegalArgumentException("Rule cannot be null");
        }
        
        String ruleName = rule.getName();
        if (ruleMap.containsKey(ruleName)) {
            logger.warn("Rule already registered: {}", ruleName);
            return;
        }
        
        // 初始化规则
        try {
            rule.initialize();
        } catch (Exception e) {
            logger.error("Error initializing rule: " + ruleName, e);
            return;
        }

        // 根据配置设置规则的启用状态
        try {
            RaspConfigManager configManager = RaspConfigManager.getInstance();
            boolean shouldEnable = configManager.isRuleEnabled(ruleName);
            if (shouldEnable) {
                rule.enable();
            } else {
                rule.disable();
            }
            logger.info("Rule {} configured as: {}", ruleName, shouldEnable ? "ENABLED" : "DISABLED");
        } catch (Exception e) {
            logger.warn("Failed to apply configuration for rule: {}, using default state", ruleName, e);
        }

        rules.add(rule);
        ruleMap.put(ruleName, rule);

        // 按优先级排序
        rules.sort(Comparator.comparingInt(Rule::getPriority));

        // 更新事件类型到规则的映射
        updateEventTypeRuleMapping(rule);

        logger.info("Rule registered: {} - {} (priority: {}, enabled: {})",
                   ruleName, rule.getDescription(), rule.getPriority(), rule.isEnabled());
    }
    
    /**
     * 卸载规则
     * @param ruleName 规则名称
     */
    public void unregisterRule(String ruleName) {
        Rule rule = ruleMap.remove(ruleName);
        if (rule != null) {
            rules.remove(rule);
            
            // 销毁规则
            try {
                rule.destroy();
            } catch (Exception e) {
                logger.error("Error destroying rule: " + ruleName, e);
            }
            
            // 更新事件类型到规则的映射
            removeFromEventTypeRuleMapping(rule);
            
            // 关闭对应的类加载器
            ModuleClassLoader classLoader = ruleClassLoaders.remove(ruleName);
            if (classLoader != null) {
                try {
                    classLoader.close();
                } catch (Exception e) {
                    logger.error("Error closing class loader for rule: " + ruleName, e);
                }
            }
            
            logger.debug("Rule unregistered: {}", ruleName);
        }
    }
    
    /**
     * 从JAR文件加载规则
     * @param jarFile JAR文件
     * @param ruleClassName 规则类名
     */
    public void loadRuleFromJar(File jarFile, String ruleClassName) {
        try {
            String moduleName = "rule-" + jarFile.getName();
            ModuleClassLoader classLoader = ModuleClassLoader.fromJarFile(
                moduleName, "1.0.0", jarFile, getClass().getClassLoader());
            
            Class<?> ruleClass = classLoader.loadClass(ruleClassName);
            if (!Rule.class.isAssignableFrom(ruleClass)) {
                throw new IllegalArgumentException("Class is not a Rule: " + ruleClassName);
            }
            
            Rule rule = (Rule) ruleClass.newInstance();
            registerRule(rule);
            
            ruleClassLoaders.put(rule.getName(), classLoader);
            
            logger.info("Rule loaded from jar: {} -> {}", jarFile.getName(), rule.getName());
            
        } catch (Exception e) {
            logger.error("Error loading rule from jar: " + jarFile.getAbsolutePath(), e);
            throw new RuntimeException("Failed to load rule from jar", e);
        }
    }
    
    /**
     * 处理Hook事件
     * @param event Hook事件
     * @return 规则处理结果列表
     */
    public List<RuleResult> processEvent(HookEvent event) {
        if (event == null) {
            logger.warn("[RuleEngine] Received null event");
            return Collections.emptyList();
        }

        logger.debug("[RuleEngine] Processing event: {}", event.getClass().getSimpleName());
        logger.debug("Event Type: {}", event.getEventType());
        logger.debug("Total registered rules: {}", rules.size());

        List<Rule> applicableRules = getApplicableRules(event.getEventType());
        logger.debug("Found {} applicable rules for event type: {}", applicableRules.size(), event.getEventType());

        for (Rule rule : applicableRules) {
            logger.debug("Applicable rule: {} (enabled: {})", rule.getName(), rule.isEnabled());
        }

        List<RuleResult> results = new ArrayList<>();

        for (Rule rule : applicableRules) {
            try {
                RuleResult result = rule.process(event);
                if (result != null) {
                    results.add(result);
                    
                    // 记录处理结果
                    if (result.isMatched() && result.getAction() != RuleResult.Action.LOG && result.getAction() != RuleResult.Action.ALLOW ) {
                        logger.info("Hook:{}.{}[{}] -> Rule matched: {} -> {}",event.getClassName(), event.getMethodName(), event.getMethodSignature() , rule.getName(), result);
                    }

                    if (result.isMatched() && (result.getAction() == RuleResult.Action.LOG || result.getAction() != RuleResult.Action.ALLOW) ) {
                        logger.debug("Hook:{}.{}[{}] -> Rule matched: {} -> {}",event.getClassName(), event.getMethodName(), event.getMethodSignature() , rule.getName(), result);
                    }
                    
                    // 如果是阻止动作，可以考虑是否继续执行其他规则
                    if (result.getAction() == RuleResult.Action.BLOCK) {
                        logger.debug("Rule blocked execution: {} -> {}", rule.getName(), result.getMessage());
                        // 这里可以根据需要决定是否继续执行其他规则
                    }
                }
            } catch (Exception e) {
                logger.error("Error processing event in rule: " + rule.getName(), e);
                // 继续执行其他规则
            }
        }
        
        return results;
    }
    
    /**
     * 获取适用于指定事件类型的规则
     * @param eventType 事件类型
     * @return 规则列表
     */
    private List<Rule> getApplicableRules(HookEvent.EventType eventType) {
        List<Rule> result = new ArrayList<>();
        
        // 从映射中获取直接支持的规则
        List<Rule> directRules = eventTypeRuleMap.get(eventType);
        if (directRules != null) {
            result.addAll(directRules);
        }
        
        // 检查其他规则是否支持该事件类型
        for (Rule rule : rules) {
            if (rule.isEnabled() && rule.supportsEventType(eventType)) {
                if (!result.contains(rule)) {
                    result.add(rule);
                }
            }
        }
        
        // 按优先级排序
        result.sort(Comparator.comparingInt(Rule::getPriority));
        
        return result;
    }
    
    /**
     * 更新事件类型到规则的映射
     * @param rule 规则实例
     */
    private void updateEventTypeRuleMapping(Rule rule) {
        HookEvent.EventType[] supportedTypes = rule.getSupportedEventTypes();
        if (supportedTypes != null) {
            for (HookEvent.EventType eventType : supportedTypes) {
                eventTypeRuleMap.computeIfAbsent(eventType, k -> new ArrayList<>()).add(rule);
            }
        }
    }
    
    /**
     * 从事件类型到规则的映射中移除规则
     * @param rule 规则实例
     */
    private void removeFromEventTypeRuleMapping(Rule rule) {
        Iterator<Map.Entry<HookEvent.EventType, List<Rule>>> iterator = eventTypeRuleMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<HookEvent.EventType, List<Rule>> entry = iterator.next();
            List<Rule> ruleList = entry.getValue();
            ruleList.remove(rule);
            if (ruleList.isEmpty()) {
                iterator.remove();
            }
        }
    }
    
    /**
     * 获取所有已注册的规则
     * @return 规则列表
     */
    public List<Rule> getAllRules() {
        return new ArrayList<>(rules);
    }
    
    /**
     * 根据名称获取规则
     * @param name 规则名称
     * @return 规则实例，如果不存在返回null
     */
    public Rule getRule(String name) {
        return ruleMap.get(name);
    }
    
    /**
     * 启用规则
     * @param ruleName 规则名称
     */
    public void enableRule(String ruleName) {
        Rule rule = ruleMap.get(ruleName);
        if (rule != null) {
            rule.enable();
        }
    }
    
    /**
     * 禁用规则
     * @param ruleName 规则名称
     */
    public void disableRule(String ruleName) {
        Rule rule = ruleMap.get(ruleName);
        if (rule != null) {
            rule.disable();
        }
    }
    
    /**
     * 获取规则统计信息
     * @return 统计信息映射
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalRules", rules.size());
        stats.put("enabledRules", rules.stream().mapToInt(r -> r.isEnabled() ? 1 : 0).sum());
        stats.put("disabledRules", rules.stream().mapToInt(r -> r.isEnabled() ? 0 : 1).sum());
        
        Map<HookEvent.EventType, Integer> eventTypeStats = new HashMap<>();
        for (Map.Entry<HookEvent.EventType, List<Rule>> entry : eventTypeRuleMap.entrySet()) {
            eventTypeStats.put(entry.getKey(), entry.getValue().size());
        }
        stats.put("eventTypeRules", eventTypeStats);
        
        return stats;
    }
    
    /**
     * 重新加载规则配置
     * 根据最新的配置文件重新设置所有规则的启用状态
     */
    public void reloadConfiguration() {
        try {
            logger.debug("Reloading rule configuration...");

            // 重新加载配置管理器
            RaspConfigManager configManager = RaspConfigManager.getInstance();
            configManager.reload();

            // 重新配置所有规则
            for (Rule rule : rules) {
                String ruleName = rule.getName();
                boolean shouldEnable = configManager.isRuleEnabled(ruleName);

                if (shouldEnable && !rule.isEnabled()) {
                    rule.enable();
                    logger.debug("Rule enabled by config reload: {}", ruleName);
                } else if (!shouldEnable && rule.isEnabled()) {
                    rule.disable();
                    logger.debug("Rule disabled by config reload: {}", ruleName);
                }
            }

            logger.debug("Rule configuration reload completed");

        } catch (Exception e) {
            logger.error("Failed to reload rule configuration", e);
        }
    }

    /**
     * 获取规则配置摘要
     * @return 配置摘要字符串
     */
    public String getConfigurationSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("=== Rule Configuration Summary ===\n");
        summary.append(String.format("Total rules: %d\n", rules.size()));

        int enabledCount = 0;
        for (Rule rule : rules) {
            boolean enabled = rule.isEnabled();
            if (enabled) enabledCount++;
            summary.append(String.format("- %s: %s (priority: %d)\n",
                rule.getName(),
                enabled ? "ENABLED" : "DISABLED",
                rule.getPriority()));
        }

        summary.append(String.format("Enabled: %d, Disabled: %d\n", enabledCount, rules.size() - enabledCount));
        summary.append("=== End Summary ===");

        return summary.toString();
    }

    /**
     * 清理所有规则
     */
    public void clear() {
        // 销毁所有规则
        for (Rule rule : rules) {
            try {
                rule.destroy();
            } catch (Exception e) {
                logger.error("Error destroying rule: " + rule.getName(), e);
            }
        }

        rules.clear();
        ruleMap.clear();
        eventTypeRuleMap.clear();

        // 关闭所有类加载器
        for (ModuleClassLoader classLoader : ruleClassLoaders.values()) {
            try {
                classLoader.close();
            } catch (Exception e) {
                logger.error("Error closing class loader", e);
            }
        }
        ruleClassLoaders.clear();

        logger.debug("All rules cleared");
    }
}
