package com.rasp.core.classloader;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLClassLoader;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 模块类加载器
 * 用于加载Hook和Rule模块，实现模块间的类隔离
 */
public class ModuleClassLoader extends URLClassLoader {
    
    private static final Logger logger = LoggerFactory.getLogger(ModuleClassLoader.class);
    
    /**
     * 模块名称
     */
    private final String moduleName;
    
    /**
     * 模块版本
     */
    private final String moduleVersion;
    
    /**
     * 类缓存
     */
    private final ConcurrentHashMap<String, Class<?>> classCache = new ConcurrentHashMap<>();
    
    /**
     * 需要委派给父类加载器的类前缀
     */
    private static final String[] PARENT_DELEGATE_PREFIXES = {
        "java.",
        "javax.",
        "sun.",
        "com.sun.",
        "org.xml.",
        "org.w3c.",
        "com.rasp.api.",  // API包由父类加载器加载，确保接口一致性
        "org.slf4j.",
        "ch.qos.logback."
    };
    
    public ModuleClassLoader(String moduleName, String moduleVersion, URL[] urls, ClassLoader parent) {
        super(urls, parent);
        this.moduleName = moduleName;
        this.moduleVersion = moduleVersion;
        logger.info("ModuleClassLoader created for module: {} version: {}", moduleName, moduleVersion);
    }
    
    /**
     * 从JAR文件创建模块类加载器
     * @param moduleName 模块名称
     * @param moduleVersion 模块版本
     * @param jarFile JAR文件
     * @param parent 父类加载器
     * @return 模块类加载器
     */
    public static ModuleClassLoader fromJarFile(String moduleName, String moduleVersion, 
                                               File jarFile, ClassLoader parent) {
        try {
            URL[] urls = {jarFile.toURI().toURL()};
            return new ModuleClassLoader(moduleName, moduleVersion, urls, parent);
        } catch (MalformedURLException e) {
            throw new RuntimeException("Invalid jar file: " + jarFile.getAbsolutePath(), e);
        }
    }
    
    @Override
    protected Class<?> loadClass(String name, boolean resolve) throws ClassNotFoundException {
        synchronized (getClassLoadingLock(name)) {
            // 首先检查类是否已经被加载
            Class<?> clazz = classCache.get(name);
            if (clazz != null) {
                if (resolve) {
                    resolveClass(clazz);
                }
                return clazz;
            }
            
            // 检查是否需要委派给父类加载器
            if (shouldDelegateToParent(name)) {
                try {
                    clazz = getParent().loadClass(name);
                    if (resolve) {
                        resolveClass(clazz);
                    }
                    classCache.put(name, clazz);
                    return clazz;
                } catch (ClassNotFoundException e) {
                    // 父类加载器无法加载，继续尝试自己加载
                }
            }
            
            // 尝试自己加载类
            try {
                clazz = findClass(name);
                if (resolve) {
                    resolveClass(clazz);
                }
                classCache.put(name, clazz);
                return clazz;
            } catch (ClassNotFoundException e) {
                // 自己无法加载，委派给父类加载器
                if (!shouldDelegateToParent(name)) {
                    try {
                        clazz = getParent().loadClass(name);
                        if (resolve) {
                            resolveClass(clazz);
                        }
                        classCache.put(name, clazz);
                        return clazz;
                    } catch (ClassNotFoundException ex) {
                        // 父类加载器也无法加载
                    }
                }
                throw e;
            }
        }
    }
    
    /**
     * 判断是否应该委派给父类加载器
     * @param className 类名
     * @return true表示应该委派给父类加载器
     */
    private boolean shouldDelegateToParent(String className) {
        for (String prefix : PARENT_DELEGATE_PREFIXES) {
            if (className.startsWith(prefix)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 获取模块名称
     * @return 模块名称
     */
    public String getModuleName() {
        return moduleName;
    }
    
    /**
     * 获取模块版本
     * @return 模块版本
     */
    public String getModuleVersion() {
        return moduleVersion;
    }
    
    /**
     * 清理类缓存
     */
    public void clearCache() {
        classCache.clear();
        logger.info("Class cache cleared for module: {}", moduleName);
    }
    
    @Override
    public void close() throws java.io.IOException {
        clearCache();
        super.close();
        logger.info("ModuleClassLoader closed for module: {}", moduleName);
    }
    
    @Override
    public String toString() {
        return "ModuleClassLoader{" +
                "moduleName='" + moduleName + '\'' +
                ", moduleVersion='" + moduleVersion + '\'' +
                '}';
    }
}
