package com.rasp.api.rule;

import com.rasp.api.event.HookEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 规则抽象基类
 * 提供规则的通用实现
 */
public abstract class AbstractRule implements Rule {
    
    protected final Logger logger = LoggerFactory.getLogger(getClass());
    
    /**
     * 规则是否启用
     */
    private volatile boolean enabled = true;
    
    /**
     * 支持的事件类型集合
     */
    private Set<HookEvent.EventType> supportedEventTypeSet;
    
    public AbstractRule() {
        HookEvent.EventType[] types = getSupportedEventTypes();
        if (types != null) {
            supportedEventTypeSet = new HashSet<>(Arrays.asList(types));
        } else {
            supportedEventTypeSet = new HashSet<>();
        }
    }
    
    @Override
    public boolean supportsEventType(HookEvent.EventType eventType) {
        return supportedEventTypeSet.contains(eventType);
    }
    
    @Override
    public RuleResult process(HookEvent event) {
        if (!enabled) {
            return RuleResult.allow(getName());
        }
        
        if (!supportsEventType(event.getEventType())) {
            return RuleResult.allow(getName());
        }
        
        try {
            return doProcess(event);
        } catch (Exception e) {
            logger.error("Error processing event in rule: " + getName(), e);
            return RuleResult.allow(getName());
        }
    }
    
    /**
     * 具体的规则处理逻辑
     * 子类需要实现此方法
     * @param event Hook事件
     * @return 规则处理结果
     */
    protected abstract RuleResult doProcess(HookEvent event);
    
    @Override
    public boolean isEnabled() {
        return enabled;
    }
    
    @Override
    public void enable() {
        this.enabled = true;
        logger.info("Rule enabled: {}", getName());
    }
    
    @Override
    public void disable() {
        this.enabled = false;
        logger.info("Rule disabled: {}", getName());
    }
    
    @Override
    public void initialize() {
        logger.info("Rule initialized: {}", getName());
    }
    
    @Override
    public void destroy() {
        logger.info("Rule destroyed: {}", getName());
    }
    
    @Override
    public String getVersion() {
        return "1.0.0";
    }
    
    @Override
    public int getPriority() {
        return 100; // 默认优先级
    }
    
    /**
     * 格式化堆栈信息
     * @param stackTrace 堆栈数组
     * @return 格式化后的堆栈字符串
     */
    protected String formatStackTrace(StackTraceElement[] stackTrace) {
        if (stackTrace == null || stackTrace.length == 0) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder();
        for (StackTraceElement element : stackTrace) {
            sb.append("\tat ").append(element.toString()).append("\n");
        }
        return sb.toString();
    }
    
    /**
     * 格式化参数信息
     * @param arguments 参数数组
     * @return 格式化后的参数字符串
     */
    protected String formatArguments(Object[] arguments) {
        if (arguments == null || arguments.length == 0) {
            return "[]";
        }
        
        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < arguments.length; i++) {
            if (i > 0) {
                sb.append(", ");
            }
            Object arg = arguments[i];
            if (arg == null) {
                sb.append("null");
            } else if (arg instanceof String) {
                sb.append("\"").append(arg).append("\"");
            } else {
                sb.append(arg.toString());
            }
        }
        sb.append("]");
        return sb.toString();
    }
}
