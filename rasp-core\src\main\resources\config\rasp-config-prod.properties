# RASP Agent Configuration File - Production Environment
# This configuration is optimized for production performance and security

# ============================================================================
# Global Configuration
# ============================================================================

rasp.enabled=true
rasp.log.level=WARN
rasp.log.file=logs/rasp-agent-prod.log

# ============================================================================
# Rule Configuration - Production Mode
# ============================================================================

# HTTP Request Logging Rule - ENABLED but less verbose in production
rasp.rule.HttpRequestLogRule.enabled=true

# Command Execution Security Rule - ENABLED for security protection
rasp.rule.CommandExecutionRule.enabled=true

# File Write Monitoring Rule - ENABLED for security monitoring
rasp.rule.FileWriteRule.enabled=true

# File Read Monitoring Rule - ENABLED for security monitoring
rasp.rule.FileReadRule.enabled=true

# Method Call Logging Rule - DISABLED in production (too verbose)
rasp.rule.MethodCallLogRule.enabled=false
