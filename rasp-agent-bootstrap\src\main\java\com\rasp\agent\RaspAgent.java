package com.rasp.agent;

import java.lang.instrument.Instrumentation;
import java.net.URL;
import java.net.URLClassLoader;
import java.io.File;
import java.lang.reflect.Method;

import java.lang.instrument.Instrumentation;

/**
 * RASP Agent入口类
 * Java Agent的主入口点
 */
public class RaspAgent {
    
    // 使用System.out.println避免日志框架冲突

    /**
     * Agent版本
     */
    private static final String VERSION = "1.0.0";

    /**
     * 简单的日志方法，避免日志框架冲突
     */
    private static void log(String level, String message) {
        String timestamp = java.time.LocalDateTime.now().toString();
        System.out.println(String.format("[%s] [%s] [RASP-Agent] %s", timestamp, level, message));
    }

    private static void logInfo(String message) {
        log("INFO", message);
    }

    private static void logError(String message, Throwable t) {
        log("ERROR", message + ": " + t.getMessage());
        if (t != null) {
            t.printStackTrace();
        }
    }

    private static void logWarn(String message) {
        log("WARN", message);
    }
    
    /**
     * premain方法 - JVM启动时加载Agent
     * @param agentArgs Agent参数
     * @param inst Instrumentation实例
     */
    public static void premain(String agentArgs, Instrumentation inst) {
        printBanner();
        logInfo("RASP Agent starting with premain...");
        logInfo("Agent arguments: " + agentArgs);

        try {
            // 首先将Spy类添加到Bootstrap ClassPath
            addSpyToBootstrapClassPath(inst);

            // 创建隔离的类加载器并初始化RASP核心
            initializeWithIsolatedClassLoader(inst, agentArgs);

            logInfo("RASP Agent started successfully");

        } catch (Exception e) {
            logError("Failed to start RASP Agent", e);
        }
    }
    
    /**
     * agentmain方法 - 运行时动态加载Agent
     * @param agentArgs Agent参数
     * @param inst Instrumentation实例
     */
    public static void agentmain(String agentArgs, Instrumentation inst) {
        printBanner();
        logInfo("RASP Agent starting with agentmain...");
        logInfo("Agent arguments: " + agentArgs);

        try {
            // 创建隔离的类加载器并初始化RASP核心
            initializeWithIsolatedClassLoader(inst, agentArgs);

            logInfo("RASP Agent attached successfully");

        } catch (Exception e) {
            logError("Failed to attach RASP Agent", e);
        }
    }

    /**
     * 将Spy类添加到Bootstrap ClassPath
     * @param inst Instrumentation实例
     */
    private static void addSpyToBootstrapClassPath(Instrumentation inst) {
        try {
            // 获取当前Agent JAR的路径
            String agentJarPath = RaspAgent.class.getProtectionDomain()
                    .getCodeSource().getLocation().getPath();

            // 解码URL路径
            agentJarPath = java.net.URLDecoder.decode(agentJarPath, "UTF-8");

            // 将Agent JAR添加到Bootstrap ClassPath
            // 新的Spy类已经简化，不依赖外部组件，应该能安全加载
            java.io.File agentJarFile = new java.io.File(agentJarPath);
            if (agentJarFile.exists()) {
                inst.appendToBootstrapClassLoaderSearch(new java.util.jar.JarFile(agentJarFile));
                logInfo("Added Agent JAR to Bootstrap ClassPath: " + agentJarPath);

                // 验证Spy类是否可以加载
                try {
                    Class.forName("com.rasp.core.spy.Spy", false, null);
                    logInfo("Spy class successfully accessible from Bootstrap ClassLoader");
                } catch (ClassNotFoundException e) {
                    logWarn("Spy class not found in Bootstrap ClassPath: " + e.getMessage());
                }
            } else {
                logWarn("Agent JAR file not found: " + agentJarPath);
            }

        } catch (Exception e) {
            logError("Failed to add Spy classes to Bootstrap ClassPath", e);
        }
    }

    /**
     * 打印启动横幅
     */
    private static void printBanner() {
        String banner = 
            "\n" +
            "██████╗  █████╗ ███████╗██████╗      █████╗  ██████╗ ███████╗███╗   ██╗████████╗\n" +
            "██╔══██╗██╔══██╗██╔════╝██╔══██╗    ██╔══██╗██╔════╝ ██╔════╝████╗  ██║╚══██╔══╝\n" +
            "██████╔╝███████║███████╗██████╔╝    ███████║██║  ███╗█████╗  ██╔██╗ ██║   ██║   \n" +
            "██╔══██╗██╔══██║╚════██║██╔═══╝     ██╔══██║██║   ██║██╔══╝  ██║╚██╗██║   ██║   \n" +
            "██║  ██║██║  ██║███████║██║         ██║  ██║╚██████╔╝███████╗██║ ╚████║   ██║   \n" +
            "╚═╝  ╚═╝╚═╝  ╚═╝╚══════╝╚═╝         ╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝   ╚═╝   \n" +
            "\n" +
            "Runtime Application Self-Protection Agent v" + VERSION + "\n" +
            "========================================================================\n";
        
        System.out.println(banner);
    }
    

    
    /**
     * 获取Agent版本
     * @return 版本号
     */
    public static String getVersion() {
        return VERSION;
    }

    /**
     * 使用隔离的类加载器初始化RASP核心
     * 这样可以完全避免与应用的依赖冲突
     */
    private static void initializeWithIsolatedClassLoader(Instrumentation inst, String agentArgs) throws Exception {
        // 获取当前Agent JAR的路径
        String agentJarPath = RaspAgent.class.getProtectionDomain()
                .getCodeSource().getLocation().getPath();
        agentJarPath = java.net.URLDecoder.decode(agentJarPath, "UTF-8");

        // 创建隔离的类加载器
        // 父类加载器设置为系统类加载器，但是使用双亲委派的反向模式
        File agentJarFile = new File(agentJarPath);
        URL agentJarUrl = agentJarFile.toURI().toURL();

        // 创建一个自定义的隔离类加载器
        IsolatedClassLoader isolatedClassLoader = new IsolatedClassLoader(
            new URL[]{agentJarUrl},
            ClassLoader.getSystemClassLoader()
        );

        logInfo("Created isolated ClassLoader for RASP core");

        // 在隔离的类加载器中加载并初始化RaspCore
        Class<?> raspCoreClass = isolatedClassLoader.loadClass("com.rasp.core.RaspCore");
        Method initializeMethod = raspCoreClass.getMethod("initialize", Instrumentation.class, String.class);

        // 调用初始化方法
        initializeMethod.invoke(null, inst, agentArgs);

        // 注册关闭钩子
        registerShutdownHookWithIsolatedClassLoader(isolatedClassLoader, raspCoreClass);

        logInfo("RASP core initialized successfully in isolated ClassLoader");
    }

    /**
     * 注册关闭钩子（使用隔离的类加载器）
     */
    private static void registerShutdownHookWithIsolatedClassLoader(ClassLoader isolatedClassLoader, Class<?> raspCoreClass) {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            logInfo("RASP Agent shutting down...");
            try {
                Method destroyMethod = raspCoreClass.getMethod("destroy");
                destroyMethod.invoke(null);
                logInfo("RASP Agent shutdown completed");
            } catch (Exception e) {
                logError("Error during RASP Agent shutdown", e);
            }
        }, "RASP-Shutdown-Hook"));
    }

    /**
     * 自定义的隔离类加载器
     * 优先从自己的JAR中加载类，避免与应用的类冲突
     */
    private static class IsolatedClassLoader extends URLClassLoader {

        public IsolatedClassLoader(URL[] urls, ClassLoader parent) {
            super(urls, parent);
        }

        @Override
        protected Class<?> loadClass(String name, boolean resolve) throws ClassNotFoundException {
            // 对于RASP相关的类，优先从自己的JAR中加载
            if (name.startsWith("com.rasp.") ||
                name.startsWith("ch.qos.logback.") ||
                name.startsWith("org.slf4j.")) {

                // 先检查是否已经加载过
                Class<?> loadedClass = findLoadedClass(name);
                if (loadedClass != null) {
                    return loadedClass;
                }

                try {
                    // 尝试从自己的JAR中加载
                    loadedClass = findClass(name);
                    if (resolve) {
                        resolveClass(loadedClass);
                    }
                    return loadedClass;
                } catch (ClassNotFoundException e) {
                    // 如果找不到，则委派给父类加载器
                }
            }

            // 对于其他类，使用标准的双亲委派模式
            return super.loadClass(name, resolve);
        }
    }
}
