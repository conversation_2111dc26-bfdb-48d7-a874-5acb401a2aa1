package com.rasp.core.config;

import com.rasp.core.rule.RuleEngine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 规则配置工具类
 * 提供便捷的规则配置管理功能
 */
public class RuleConfigUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(RuleConfigUtil.class);
    
    /**
     * 打印当前规则配置状态
     */
    public static void printRuleStatus() {
        try {
            RuleEngine ruleEngine = RuleEngine.getInstance();
            String summary = ruleEngine.getConfigurationSummary();
            System.out.println(summary);
            logger.info("Rule status printed to console");
        } catch (Exception e) {
            logger.error("Failed to print rule status", e);
        }
    }
    
    /**
     * 启用指定规则
     * @param ruleName 规则名称
     */
    public static void enableRule(String ruleName) {
        try {
            RuleEngine ruleEngine = RuleEngine.getInstance();
            ruleEngine.enableRule(ruleName);
            logger.info("Rule enabled via utility: {}", ruleName);
        } catch (Exception e) {
            logger.error("Failed to enable rule: {}", ruleName, e);
        }
    }
    
    /**
     * 禁用指定规则
     * @param ruleName 规则名称
     */
    public static void disableRule(String ruleName) {
        try {
            RuleEngine ruleEngine = RuleEngine.getInstance();
            ruleEngine.disableRule(ruleName);
            logger.info("Rule disabled via utility: {}", ruleName);
        } catch (Exception e) {
            logger.error("Failed to disable rule: {}", ruleName, e);
        }
    }
    
    /**
     * 重新加载配置文件
     */
    public static void reloadConfiguration() {
        try {
            RuleEngine ruleEngine = RuleEngine.getInstance();
            ruleEngine.reloadConfiguration();
            logger.info("Configuration reloaded via utility");
        } catch (Exception e) {
            logger.error("Failed to reload configuration", e);
        }
    }
    
    /**
     * 批量启用规则
     * @param ruleNames 规则名称数组
     */
    public static void enableRules(String... ruleNames) {
        for (String ruleName : ruleNames) {
            enableRule(ruleName);
        }
    }
    
    /**
     * 批量禁用规则
     * @param ruleNames 规则名称数组
     */
    public static void disableRules(String... ruleNames) {
        for (String ruleName : ruleNames) {
            disableRule(ruleName);
        }
    }
    
    /**
     * 启用所有日志类规则
     */
    public static void enableAllLogRules() {
        enableRules("HttpRequestLogRule", "MethodCallLogRule");
        logger.info("All log rules enabled");
    }
    
    /**
     * 禁用所有日志类规则
     */
    public static void disableAllLogRules() {
        disableRules("HttpRequestLogRule", "MethodCallLogRule");
        logger.info("All log rules disabled");
    }
    
    /**
     * 启用所有安全类规则
     */
    public static void enableAllSecurityRules() {
        enableRules("CommandExecutionRule", "FileWriteRule");
        logger.info("All security rules enabled");
    }
    
    /**
     * 禁用所有安全类规则
     */
    public static void disableAllSecurityRules() {
        disableRules("CommandExecutionRule", "FileWriteRule");
        logger.info("All security rules disabled");
    }
    
    /**
     * 设置开发模式配置
     * 启用详细日志和调试规则
     */
    public static void setDevelopmentMode() {
        logger.info("Setting development mode configuration...");
        enableRules("HttpRequestLogRule", "MethodCallLogRule", "CommandExecutionRule", "FileWriteRule");
        logger.info("Development mode configuration applied");
    }
    
    /**
     * 设置生产模式配置
     * 禁用详细日志，只保留安全规则
     */
    public static void setProductionMode() {
        logger.info("Setting production mode configuration...");
        disableRules("MethodCallLogRule");
        enableRules("HttpRequestLogRule", "CommandExecutionRule", "FileWriteRule");
        logger.info("Production mode configuration applied");
    }
    
    /**
     * 检查规则是否存在
     * @param ruleName 规则名称
     * @return true表示规则存在
     */
    public static boolean ruleExists(String ruleName) {
        try {
            RuleEngine ruleEngine = RuleEngine.getInstance();
            return ruleEngine.getRule(ruleName) != null;
        } catch (Exception e) {
            logger.error("Failed to check rule existence: {}", ruleName, e);
            return false;
        }
    }
    
    /**
     * 检查规则是否启用
     * @param ruleName 规则名称
     * @return true表示规则启用
     */
    public static boolean isRuleEnabled(String ruleName) {
        try {
            RuleEngine ruleEngine = RuleEngine.getInstance();
            com.rasp.api.rule.Rule rule = ruleEngine.getRule(ruleName);
            return rule != null && rule.isEnabled();
        } catch (Exception e) {
            logger.error("Failed to check rule status: {}", ruleName, e);
            return false;
        }
    }
    
    /**
     * 获取所有规则名称
     * @return 规则名称数组
     */
    public static String[] getAllRuleNames() {
        try {
            RuleEngine ruleEngine = RuleEngine.getInstance();
            return ruleEngine.getAllRules().stream()
                    .map(rule -> rule.getName())
                    .toArray(String[]::new);
        } catch (Exception e) {
            logger.error("Failed to get all rule names", e);
            return new String[0];
        }
    }
    
    /**
     * 获取启用的规则名称
     * @return 启用的规则名称数组
     */
    public static String[] getEnabledRuleNames() {
        try {
            RuleEngine ruleEngine = RuleEngine.getInstance();
            return ruleEngine.getAllRules().stream()
                    .filter(rule -> rule.isEnabled())
                    .map(rule -> rule.getName())
                    .toArray(String[]::new);
        } catch (Exception e) {
            logger.error("Failed to get enabled rule names", e);
            return new String[0];
        }
    }
    
    /**
     * 获取禁用的规则名称
     * @return 禁用的规则名称数组
     */
    public static String[] getDisabledRuleNames() {
        try {
            RuleEngine ruleEngine = RuleEngine.getInstance();
            return ruleEngine.getAllRules().stream()
                    .filter(rule -> !rule.isEnabled())
                    .map(rule -> rule.getName())
                    .toArray(String[]::new);
        } catch (Exception e) {
            logger.error("Failed to get disabled rule names", e);
            return new String[0];
        }
    }
    
    /**
     * 验证配置文件格式
     * @return true表示配置文件格式正确
     */
    public static boolean validateConfiguration() {
        try {
            RaspConfigManager configManager = RaspConfigManager.getInstance();
            // 这里可以添加更多的验证逻辑
            logger.info("Configuration validation completed");
            return true;
        } catch (Exception e) {
            logger.error("Configuration validation failed", e);
            return false;
        }
    }
}
